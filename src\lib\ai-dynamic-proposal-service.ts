/**
 * AI-powered dynamic proposal generation service
 * Integrates OpenAI with the dynamic proposal generator for real-time customization
 */

import { generateDynamicProposal, DynamicProposalContent, CustomerAnalysis } from './dynamic-proposal-generator';
import { EnhancedProposal } from '@/types/enhanced-proposal-schema';

export interface AIProposalRequest {
  customerInfo: string;
  businessName?: string;
  businessType?: string;
  existingProposal?: EnhancedProposal;
  specificQuestions?: string[];
}

export interface AIProposalResponse {
  dynamicContent: DynamicProposalContent;
  updatedProposal: EnhancedProposal;
  analysis: CustomerAnalysis;
  aiEnhancements: {
    improvedGreeting: string;
    enhancedSolutions: string[];
    customizedNextSteps: string[];
  };
}

/**
 * Generates a complete dynamic proposal using AI enhancement
 */
export async function generateAIEnhancedProposal(request: AIProposalRequest): Promise<AIProposalResponse> {
  // Step 1: Generate base dynamic content
  const dynamicContent = generateDynamicProposal(request.customerInfo);
  
  // Step 2: Use AI to enhance the content
  const aiEnhancements = await enhanceProposalWithAI(dynamicContent, request);
  
  // Step 3: Create or update the enhanced proposal
  const updatedProposal = createEnhancedProposalFromDynamic(
    dynamicContent, 
    request.existingProposal,
    aiEnhancements
  );
  
  // Step 4: Extract analysis for reference
  const analysis = extractAnalysisFromDynamic(dynamicContent);
  
  return {
    dynamicContent,
    updatedProposal,
    analysis,
    aiEnhancements
  };
}

/**
 * Uses OpenAI to enhance the generated proposal content
 */
async function enhanceProposalWithAI(
  dynamicContent: DynamicProposalContent, 
  request: AIProposalRequest
): Promise<{
  improvedGreeting: string;
  enhancedSolutions: string[];
  customizedNextSteps: string[];
}> {
  const prompt = `
You are an expert proposal writer for GetFound, a digital marketing company specializing in local businesses.

CONTEXT:
- Business: ${request.businessName || 'the client'}
- Type: ${request.businessType || 'local business'}
- Current greeting: ${dynamicContent.clientGreeting}
- Current solutions: ${dynamicContent.specificSolutions.join(', ')}
- Customer info: ${request.customerInfo}

TASK: Enhance this proposal content to be more personalized and compelling.

1. IMPROVE THE GREETING:
Make it more personal and specific to their business challenges. Reference specific details from their input.

2. ENHANCE SOLUTIONS:
Add 2-3 more specific solutions based on their unique situation. Be concrete and actionable.

3. CUSTOMIZE NEXT STEPS:
Make the next steps more specific to their business type and goals.

REQUIREMENTS:
- Keep the professional tone
- Be specific to their business type
- Reference their actual challenges/goals
- Make it feel personally written for them
- Stay focused on GetFound's services

Respond in JSON format:
{
  "improvedGreeting": "enhanced greeting text",
  "enhancedSolutions": ["solution 1", "solution 2", "solution 3"],
  "customizedNextSteps": ["step 1", "step 2", "step 3", "step 4"]
}
`;

  try {
    // For now, return enhanced content based on the dynamic analysis
    // TODO: Replace with actual OpenAI API call
    return {
      improvedGreeting: enhanceGreeting(dynamicContent, request),
      enhancedSolutions: enhanceSolutions(dynamicContent, request),
      customizedNextSteps: enhanceNextSteps(dynamicContent, request)
    };
  } catch (error) {
    console.error('AI enhancement failed, using dynamic content:', error);
    return {
      improvedGreeting: dynamicContent.clientGreeting,
      enhancedSolutions: dynamicContent.specificSolutions,
      customizedNextSteps: dynamicContent.nextSteps
    };
  }
}

/**
 * Creates an EnhancedProposal from dynamic content
 */
function createEnhancedProposalFromDynamic(
  dynamicContent: DynamicProposalContent,
  existingProposal?: EnhancedProposal,
  aiEnhancements?: any
): EnhancedProposal {
  const base = existingProposal || {
    clientName: '',
    businessName: '',
    businessType: '',
    packages: []
  };

  return {
    ...base,
    overview: {
      projectTitle: dynamicContent.projectTitle,
      introduction: aiEnhancements?.improvedGreeting || dynamicContent.clientGreeting,
      summary: dynamicContent.situationAnalysis
    },
    clientGoals: {
      title: "Understanding Your Goals",
      description: dynamicContent.goalsSection,
      goals: extractGoalsFromContent(dynamicContent.goalsSection)
    },
    packages: createPackagesFromDynamic(dynamicContent),
    specificQuestions: {
      title: "Addressing Your Specific Questions",
      questions: dynamicContent.questionsAndAnswers.map(qa => ({
        question: qa.question,
        answer: qa.answer
      }))
    },
    nextSteps: aiEnhancements?.customizedNextSteps || dynamicContent.nextSteps
  };
}

/**
 * Creates package data from dynamic content
 */
function createPackagesFromDynamic(dynamicContent: DynamicProposalContent) {
  const packages = [
    {
      name: 'Basic "GetFound" Package',
      features: dynamicContent.customizedFeatures.basic,
      setupFee: '$499',
      monthlyFee: '$100',
      isRecommended: dynamicContent.packageRecommendation.recommended === 'basic'
    },
    {
      name: 'Premium "GetFound" SEO & Growth Package',
      features: dynamicContent.customizedFeatures.premium,
      setupFee: '$2,999',
      monthlyFee: '$150',
      isRecommended: dynamicContent.packageRecommendation.recommended === 'premium'
    }
  ];

  return packages;
}

/**
 * Extracts goals from the goals section text
 */
function extractGoalsFromContent(goalsSection: string): string[] {
  const lines = goalsSection.split('\n');
  return lines
    .filter(line => line.trim().startsWith('•'))
    .map(line => line.replace('•', '').trim());
}

/**
 * Extracts analysis data from dynamic content
 */
function extractAnalysisFromDynamic(dynamicContent: DynamicProposalContent): CustomerAnalysis {
  return {
    businessName: extractBusinessNameFromTitle(dynamicContent.projectTitle),
    businessType: 'local business', // This would be extracted from the analysis
    businessCategory: 'service',
    currentSituation: dynamicContent.situationAnalysis,
    targetMarkets: ['local customers'],
    keyGoals: extractGoalsFromContent(dynamicContent.goalsSection),
    challenges: [],
    businessMaturity: 'growing',
    recommendedPackage: dynamicContent.packageRecommendation.recommended,
    confidence: 0.8,
    reasoning: dynamicContent.packageRecommendation.whyThisPackage
  };
}

function extractBusinessNameFromTitle(title: string): string {
  const match = title.match(/for (.+)$/);
  return match ? match[1] : 'Your Business';
}

// Enhanced content functions (temporary until OpenAI integration)
function enhanceGreeting(dynamicContent: DynamicProposalContent, request: AIProposalRequest): string {
  return dynamicContent.clientGreeting + " We've carefully reviewed your specific needs and challenges to create this customized proposal.";
}

function enhanceSolutions(dynamicContent: DynamicProposalContent, request: AIProposalRequest): string[] {
  return [
    ...dynamicContent.specificSolutions,
    "Dedicated project manager to ensure smooth implementation",
    "Regular progress updates and milestone check-ins",
    "Post-launch optimization and performance monitoring"
  ];
}

function enhanceNextSteps(dynamicContent: DynamicProposalContent, request: AIProposalRequest): string[] {
  return [
    "Schedule a 30-minute discovery call to discuss your specific requirements",
    "Review and select the package that best fits your business goals",
    "Complete the project agreement and initial setup payment",
    "Begin the onboarding process with our specialized team",
    "Launch your new professional online presence within 2-3 weeks"
  ];
}
