#!/usr/bin/env python3
"""
Startup script for GetFound AI Proposal Service
Handles environment setup and service startup
"""

import os
import sys
import subprocess
from pathlib import Path
from dotenv import load_dotenv

def check_environment():
    """Check if environment is properly configured"""
    print("🔍 Checking environment configuration...")
    
    # Load environment variables
    load_dotenv()
    
    # Check for required environment variables
    required_vars = ['OPENAI_API_KEY']
    missing_vars = []
    
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print("❌ Missing required environment variables:")
        for var in missing_vars:
            print(f"   - {var}")
        print("\n📝 Please edit .env file and add the missing variables")
        return False
    
    print("✅ Environment configuration is valid")
    return True

def check_dependencies():
    """Check if required dependencies are installed"""
    print("🔍 Checking dependencies...")
    
    required_packages = [
        'fastapi',
        'uvicorn',
        'pydantic_ai',
        'logfire',
        'openai'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ Missing required packages:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\n📝 Install missing packages with:")
        print("   pip install -r requirements.txt")
        return False
    
    print("✅ All dependencies are installed")
    return True

def start_service():
    """Start the AI proposal service"""
    print("🚀 Starting GetFound AI Proposal Service...")
    
    try:
        # Import and run the service
        from main import app
        import uvicorn
        
        print("✅ Service modules loaded successfully")
        print("🌐 Starting server at http://localhost:8000")
        print("📚 API documentation at http://localhost:8000/docs")
        print("🛑 Press Ctrl+C to stop the service")
        
        uvicorn.run(
            "main:app",
            host="0.0.0.0",
            port=8000,
            reload=True,
            log_level="info"
        )
        
    except ImportError as e:
        print(f"❌ Failed to import service modules: {e}")
        print("📝 Make sure all dependencies are installed")
        return False
    except Exception as e:
        print(f"❌ Failed to start service: {e}")
        return False

def main():
    """Main startup function"""
    print("🚀 GetFound AI Proposal Service")
    print("=" * 50)
    
    # Change to the script directory
    script_dir = Path(__file__).parent
    os.chdir(script_dir)
    
    # Check environment
    if not check_environment():
        sys.exit(1)
    
    # Check dependencies
    if not check_dependencies():
        sys.exit(1)
    
    # Start the service
    start_service()

if __name__ == "__main__":
    main()
