/**
 * TypeScript service for interacting with Pydantic AI proposal agent
 * Provides type-safe interface to Python AI agent
 */

// TypeScript interfaces matching Python Pydantic models
export interface CustomerAnalysis {
  business_name: string;
  current_situation: string;
  key_needs: string[];
  business_maturity: 'startup' | 'growing' | 'established';
}

export interface PackageRecommendation {
  recommended: 'basic' | 'premium';
  confidence: number;
  reasoning: string[];
  why_this_package: string[];
}

export interface QuestionAnswer {
  question: string;
  answer: string;
  category: 'website' | 'google' | 'goals' | 'pricing' | 'process' | 'general';
}

export interface ProposalContent {
  project_title: string;
  acknowledgment: string;
  package_recommendation: PackageRecommendation;
  questions_and_answers: QuestionAnswer[];
  next_steps: string[];
}

// Updated DynamicProposalContent to match new structure
export interface DynamicProposalContent {
  projectTitle: string;
  acknowledgment: string;
  packageRecommendation: {
    recommended: 'basic' | 'premium';
    reasoning: string;
    whyThisPackage: string[];
    confidence: number;
  };
  questionsAndAnswers: QuestionAnswer[];
  nextSteps: string[];
}

/**
 * Service class for Pydantic AI integration
 */
export class PydanticAIProposalService {
  private pythonServiceUrl: string;

  constructor() {
    // In production, this would be your Python service endpoint
    // For development, we'll use a local Python server or subprocess
    this.pythonServiceUrl = process.env.PYTHON_AI_SERVICE_URL || 'http://localhost:8000';
  }

  /**
   * Generate a complete proposal using Pydantic AI
   */
  async generateProposal(customerInput: string): Promise<DynamicProposalContent> {
    try {
      console.log('Calling Pydantic AI agent for proposal generation...');
      
      // For now, we'll use a fallback implementation until Python service is set up
      // TODO: Replace with actual Python service call
      const proposalContent = await this.callPythonAgent(customerInput);
      
      // Convert Python response to TypeScript format
      return this.convertToTypeScriptFormat(proposalContent);
      
    } catch (error) {
      console.error('Pydantic AI service error:', error);
      // Fallback to basic analysis if AI service fails
      return this.generateFallbackProposal(customerInput);
    }
  }

  /**
   * Quick customer analysis for preview
   */
  async analyzeCustomer(customerInput: string): Promise<CustomerAnalysis> {
    try {
      // Call Python analysis function
      const response = await fetch(`${this.pythonServiceUrl}/analyze`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ customer_input: customerInput })
      });
      
      if (!response.ok) {
        throw new Error(`Analysis service error: ${response.status}`);
      }
      
      return await response.json();
      
    } catch (error) {
      console.error('Customer analysis error:', error);
      // Fallback analysis
      return this.generateFallbackAnalysis(customerInput);
    }
  }

  /**
   * Call the Python Pydantic AI agent
   */
  private async callPythonAgent(customerInput: string): Promise<ProposalContent> {
    // For development, we'll implement a subprocess call or HTTP request
    // This is where you'd integrate with your Python service
    
    try {
      const response = await fetch(`${this.pythonServiceUrl}/generate-proposal`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ customer_input: customerInput }),
        timeout: 30000 // 30 second timeout
      });
      
      if (!response.ok) {
        throw new Error(`Python agent error: ${response.status}`);
      }
      
      return await response.json();
      
    } catch (error) {
      console.error('Python agent call failed:', error);
      throw error;
    }
  }

  /**
   * Convert Python Pydantic response to TypeScript format
   */
  private convertToTypeScriptFormat(proposalContent: ProposalContent): DynamicProposalContent {
    return {
      projectTitle: proposalContent.project_title,
      acknowledgment: proposalContent.acknowledgment,
      packageRecommendation: {
        recommended: proposalContent.package_recommendation.recommended,
        reasoning: proposalContent.package_recommendation.reasoning.join(' '),
        whyThisPackage: proposalContent.package_recommendation.why_this_package,
        confidence: proposalContent.package_recommendation.confidence
      },
      questionsAndAnswers: proposalContent.questions_and_answers,
      nextSteps: proposalContent.next_steps
    };
  }

  /**
   * Fallback proposal generation if AI service is unavailable
   */
  private generateFallbackProposal(customerInput: string): DynamicProposalContent {
    const analysis = this.generateFallbackAnalysis(customerInput);
    
    return {
      projectTitle: `Modern Website & Digital Marketing Solution for ${analysis.business_name}`,
      acknowledgment: `Thank you for discussing your vision for ${analysis.business_name}. At GetFound, we specialize in helping local businesses like yours enhance their online presence and attract their ideal customers. ${analysis.current_situation}`,
      packageRecommendation: {
        recommended: analysis.business_maturity === 'established' ? 'premium' : 'basic',
        reasoning: analysis.business_maturity === 'established' 
          ? 'Your established business can benefit from advanced SEO and comprehensive features'
          : 'The basic package provides a solid foundation for your growing business',
        whyThisPackage: analysis.business_maturity === 'established'
          ? ['Advanced SEO strategy', 'Multiple service pages', 'Professional credibility features']
          : ['Cost-effective solution', 'Professional foundation', 'Room to grow'],
        confidence: 0.7
      },
      questionsAndAnswers: this.extractBasicQuestions(customerInput),
      nextSteps: [
        'Schedule a follow-up call to discuss this proposal in detail',
        'Select the package that aligns with your business goals',
        'Provide business assets and content for website development',
        'Begin implementation with our specialized team',
        'Launch your new professional online presence'
      ]
    };
  }

  /**
   * Fallback customer analysis
   */
  private generateFallbackAnalysis(customerInput: string): CustomerAnalysis {
    const lowerInput = customerInput.toLowerCase();
    
    // Extract business name
    let businessName = 'Your Business';
    if (lowerInput.includes('lowery')) {
      businessName = 'Lowery Finishing';
    }
    
    // Determine situation
    let situation = 'Looking to improve online presence';
    if (lowerInput.includes('clickfunnel') || lowerInput.includes('click funnel')) {
      situation = 'Currently using ClickFunnels but wants a professional website';
    }
    
    // Extract needs
    const needs: string[] = [];
    if (lowerInput.includes('professional') || lowerInput.includes('legit')) {
      needs.push('Professional website presence');
    }
    if (lowerInput.includes('quote')) {
      needs.push('Easier quote request process');
    }
    if (lowerInput.includes('google') || lowerInput.includes('review')) {
      needs.push('Google Business Profile optimization');
    }
    
    // Determine maturity
    let maturity: 'startup' | 'growing' | 'established' = 'growing';
    if (lowerInput.includes('review') || lowerInput.includes('established')) {
      maturity = 'established';
    } else if (lowerInput.includes('new') || lowerInput.includes('starting')) {
      maturity = 'startup';
    }
    
    return {
      business_name: businessName,
      current_situation: situation,
      key_needs: needs.length > 0 ? needs : ['Improve online presence'],
      business_maturity: maturity
    };
  }

  /**
   * Extract basic questions from customer input
   */
  private extractBasicQuestions(customerInput: string): QuestionAnswer[] {
    const questions: QuestionAnswer[] = [];
    const lowerInput = customerInput.toLowerCase();
    
    if (lowerInput.includes('legit') || lowerInput.includes('professional')) {
      questions.push({
        question: "How will you make my website look more professional?",
        answer: "We'll create a custom, professional website that moves you beyond basic page builders. Your new site will feature modern design, professional photography integration through our GetFound app, and a structure that builds trust with your customers.",
        category: 'website'
      });
    }
    
    if (lowerInput.includes('google') && lowerInput.includes('review')) {
      questions.push({
        question: "How can I better manage my Google reviews and listing?",
        answer: "We'll optimize your Google Business Profile, help you develop a review collection strategy, and ensure your listing presents your business professionally to potential customers.",
        category: 'google'
      });
    }
    
    if (lowerInput.includes('quote')) {
      questions.push({
        question: "How will the new website make it easier for customers to request quotes?",
        answer: "We'll implement streamlined quote request forms that collect all necessary information upfront, making it easier for customers to reach you and for you to provide accurate estimates quickly.",
        category: 'process'
      });
    }
    
    return questions;
  }
}

// Export singleton instance
export const pydanticAIService = new PydanticAIProposalService();
