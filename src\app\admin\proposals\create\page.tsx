'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { isAdmin } from '@/lib/auth';

// Business industry options
const INDUSTRY_OPTIONS = [
  { value: 'contractor', label: 'Contractor/Construction' },
  { value: 'restaurant', label: 'Restaurant/Cafe' },
  { value: 'retail', label: 'Retail/Shop' },
  { value: 'professional', label: 'Professional Services' },
  { value: 'healthcare', label: 'Healthcare/Medical' },
  { value: 'beauty', label: 'Beauty/Salon' },
  { value: 'fitness', label: 'Fitness/Gym' },
  { value: 'automotive', label: 'Automotive/Repair' },
  { value: 'home', label: 'Home Services' },
  { value: 'real_estate', label: 'Real Estate' },
  { value: 'dental', label: 'Dental' },
  { value: 'legal', label: 'Legal Services' },
  { value: 'accounting', label: 'Accounting/Financial' },
  { value: 'manufacturing', label: 'Manufacturing' },
  { value: 'education', label: 'Education/Training' },
  { value: 'event', label: 'Event Planning' },
  { value: 'landscaping', label: 'Landscaping/Lawn Care' },
  { value: 'cleaning', label: 'Cleaning Services' },
  { value: 'pet', label: 'Pet Services' },
  { value: 'other', label: 'Other' }
];

// Service options
const SERVICE_OPTIONS = [
  // Construction/Contractor
  { value: 'general_contracting', label: 'General Contracting', category: 'contractor' },
  { value: 'remodeling', label: 'Remodeling', category: 'contractor' },
  { value: 'roofing', label: 'Roofing', category: 'contractor' },
  { value: 'plumbing', label: 'Plumbing', category: 'contractor' },
  { value: 'electrical', label: 'Electrical', category: 'contractor' },
  { value: 'carpentry', label: 'Carpentry', category: 'contractor' },
  { value: 'painting', label: 'Painting', category: 'contractor' },
  { value: 'flooring', label: 'Flooring', category: 'contractor' },
  { value: 'landscaping', label: 'Landscaping', category: 'landscaping' },
  { value: 'lawn_care', label: 'Lawn Care', category: 'landscaping' },
  { value: 'tree_service', label: 'Tree Service', category: 'landscaping' },
  
  // Restaurants/Food
  { value: 'dining', label: 'Dining Services', category: 'restaurant' },
  { value: 'takeout', label: 'Takeout', category: 'restaurant' },
  { value: 'delivery', label: 'Delivery', category: 'restaurant' },
  { value: 'catering', label: 'Catering', category: 'restaurant' },
  { value: 'coffee', label: 'Coffee Shop', category: 'restaurant' },
  { value: 'bakery', label: 'Bakery', category: 'restaurant' },
  
  // Retail
  { value: 'clothing', label: 'Clothing & Apparel', category: 'retail' },
  { value: 'electronics', label: 'Electronics', category: 'retail' },
  { value: 'furniture', label: 'Furniture', category: 'retail' },
  { value: 'specialty', label: 'Specialty Goods', category: 'retail' },
  { value: 'grocery', label: 'Grocery', category: 'retail' },
  
  // Professional Services
  { value: 'consulting', label: 'Consulting', category: 'professional' },
  { value: 'marketing', label: 'Marketing', category: 'professional' },
  { value: 'graphic_design', label: 'Graphic Design', category: 'professional' },
  { value: 'photography', label: 'Photography', category: 'professional' },
  { value: 'legal_services', label: 'Legal Services', category: 'legal' },
  { value: 'accounting', label: 'Accounting', category: 'accounting' },
  { value: 'insurance', label: 'Insurance', category: 'professional' },
  
  // Healthcare
  { value: 'medical_practice', label: 'Medical Practice', category: 'healthcare' },
  { value: 'chiropractic', label: 'Chiropractic', category: 'healthcare' },
  { value: 'dentistry', label: 'Dentistry', category: 'dental' },
  { value: 'therapy', label: 'Therapy Services', category: 'healthcare' },
  
  // Beauty/Wellness
  { value: 'salon', label: 'Hair Salon', category: 'beauty' },
  { value: 'spa', label: 'Spa Services', category: 'beauty' },
  { value: 'barber', label: 'Barber Shop', category: 'beauty' },
  { value: 'nail_care', label: 'Nail Care', category: 'beauty' },
  { value: 'massage', label: 'Massage Therapy', category: 'beauty' },
  
  // Fitness
  { value: 'gym', label: 'Gym/Fitness Center', category: 'fitness' },
  { value: 'personal_training', label: 'Personal Training', category: 'fitness' },
  { value: 'yoga', label: 'Yoga Studio', category: 'fitness' },
  { value: 'pilates', label: 'Pilates', category: 'fitness' },
  
  // Automotive
  { value: 'auto_repair', label: 'Auto Repair', category: 'automotive' },
  { value: 'oil_change', label: 'Oil Change', category: 'automotive' },
  { value: 'tire_service', label: 'Tire Service', category: 'automotive' },
  { value: 'detailing', label: 'Auto Detailing', category: 'automotive' },
  { value: 'body_shop', label: 'Body Shop', category: 'automotive' },
  
  // Home Services
  { value: 'cleaning', label: 'Cleaning Services', category: 'cleaning' },
  { value: 'maid_service', label: 'Maid Service', category: 'cleaning' },
  { value: 'carpet_cleaning', label: 'Carpet Cleaning', category: 'cleaning' },
  { value: 'hvac', label: 'HVAC', category: 'home' },
  { value: 'pest_control', label: 'Pest Control', category: 'home' },
  { value: 'appliance_repair', label: 'Appliance Repair', category: 'home' },
  
  // Real Estate
  { value: 'real_estate_sales', label: 'Real Estate Sales', category: 'real_estate' },
  { value: 'property_management', label: 'Property Management', category: 'real_estate' },
  { value: 'home_inspection', label: 'Home Inspection', category: 'real_estate' },
  
  // Pet Services
  { value: 'pet_grooming', label: 'Pet Grooming', category: 'pet' },
  { value: 'veterinary', label: 'Veterinary', category: 'pet' },
  { value: 'pet_boarding', label: 'Pet Boarding', category: 'pet' },
  { value: 'dog_walking', label: 'Dog Walking', category: 'pet' },
  
  // General/Other
  { value: 'other', label: 'Other', category: 'other' }
];

function CreateProposalContent() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [templates, setTemplates] = useState<any[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<string>('');
  const [selectedServices, setSelectedServices] = useState<string[]>([]);
  const [filteredServices, setFilteredServices] = useState<any[]>([]);
  
  // Form data
  const [formData, setFormData] = useState({
    clientName: '',
    businessName: '',
    businessType: '',
    challenges: '',
    notes: '',
    targetAudience: '',
    competitorNames: '',
    websiteUrl: ''
  });

  // Fetch templates on mount
  useEffect(() => {
    const fetchTemplates = async () => {
      try {
        const response = await fetch('/api/list-templates');
        
        if (!response.ok) {
          throw new Error('Failed to fetch templates');
        }
        
        const data = await response.json();
        setTemplates(data.templates || []);
        
        // Set default template if available
        if (data.templates && data.templates.length > 0) {
          const defaultTemplate = data.templates.find((t: any) => t.is_default) || data.templates[0];
          setSelectedTemplate(defaultTemplate.id);
        }
      } catch (error) {
        console.error('Error fetching templates:', error);
      }
    };
    
    fetchTemplates();
  }, []);

  // Update filtered services when business type changes
  useEffect(() => {
    if (formData.businessType) {
      const services = SERVICE_OPTIONS.filter(
        service => service.category === formData.businessType || service.category === 'other'
      );
      setFilteredServices(services);
      
      // Clear selected services that don't match the new business type
      setSelectedServices(prev => 
        prev.filter(service => 
          services.some(s => s.value === service)
        )
      );
    } else {
      setFilteredServices(SERVICE_OPTIONS);
    }
  }, [formData.businessType]);

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  // Handle service selection changes
  const handleServiceChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value, checked } = e.target;
    
    if (checked) {
      setSelectedServices(prev => [...prev, value]);
    } else {
      setSelectedServices(prev => prev.filter(service => service !== value));
    }
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);
    
    try {
      // Generate a sequential reference number
      const referenceResponse = await fetch('/api/generate-reference-number', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      });

      let referenceNumber;
      if (referenceResponse.ok) {
        const refData = await referenceResponse.json();
        referenceNumber = refData.referenceNumber;
      } else {
        // Fallback to timestamp-based if API fails
        referenceNumber = `GF-${new Date().getFullYear()}-${Date.now().toString().slice(-4)}`;
      }
      
      // Create proposal object
      const proposalData = {
        client_name: formData.clientName,
        business_name: formData.businessName,
        business_type: formData.businessType,
        reference_number: referenceNumber,
        challenges: formData.challenges,
        notes: formData.notes,
        target_audience: formData.targetAudience,
        competitor_names: formData.competitorNames,
        website_url: formData.websiteUrl,
        services: selectedServices,
        template_id: selectedTemplate,
        status: 'draft'
      };
      
      // Submit to API
      const response = await fetch('/api/create-proposal', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(proposalData),
        credentials: 'include' // Important: this ensures cookies are sent with the request
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        console.error('Error response:', errorData);
        throw new Error(errorData.message || 'Failed to create proposal');
      }
      
      const data = await response.json();
      
      // Redirect to the proposal editor page
      router.push(`/admin/proposals/edit/${data.id}`);
    } catch (error: any) {
      console.error('Error creating proposal:', error);
      setError(error.message || 'Failed to create proposal. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-gray-900">Create New Proposal</h1>
        <p className="mt-1 text-sm text-gray-600">
          Fill in the details below to create a new proposal for your client.
        </p>
      </div>
      
      {error && (
        <div className="mb-6 bg-red-50 border border-red-200 text-red-600 rounded-md p-4">
          {error}
        </div>
      )}
      
      <div className="bg-white shadow rounded-lg p-6">
        <form onSubmit={handleSubmit}>
          <div className="space-y-8">
            {/* Client Information Section */}
            <div>
              <h2 className="text-lg font-medium text-gray-900 mb-4 pb-2 border-b border-gray-200">Client Information</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="clientName" className="block text-sm font-medium text-gray-700 mb-1">
                    Client Name *
                  </label>
                  <input
                    type="text"
                    id="clientName"
                    name="clientName"
                    value={formData.clientName}
                    onChange={handleInputChange}
                    required
                    className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                  />
                </div>
                
                <div>
                  <label htmlFor="businessName" className="block text-sm font-medium text-gray-700 mb-1">
                    Business Name *
                  </label>
                  <input
                    type="text"
                    id="businessName"
                    name="businessName"
                    value={formData.businessName}
                    onChange={handleInputChange}
                    required
                    className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                  />
                </div>
              </div>
              
              <div className="mt-4">
                <label htmlFor="websiteUrl" className="block text-sm font-medium text-gray-700 mb-1">
                  Current Website URL (if any)
                </label>
                <input
                  type="url"
                  id="websiteUrl"
                  name="websiteUrl"
                  value={formData.websiteUrl}
                  onChange={handleInputChange}
                  className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                  placeholder="https://example.com"
                />
              </div>
            </div>
            
            {/* Business Details Section */}
            <div>
              <h2 className="text-lg font-medium text-gray-900 mb-4 pb-2 border-b border-gray-200">Business Details</h2>
              <div className="mb-4">
                <label htmlFor="businessType" className="block text-sm font-medium text-gray-700 mb-1">
                  Business Type/Industry *
                </label>
                <select
                  id="businessType"
                  name="businessType"
                  value={formData.businessType}
                  onChange={handleInputChange}
                  required
                  className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                >
                  <option value="">Select Industry</option>
                  {INDUSTRY_OPTIONS.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>
              
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Services Offered (select all that apply)
                </label>
                <div className="bg-gray-50 p-4 rounded-md border border-gray-200">
                  {formData.businessType ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                      {filteredServices.map(option => (
                        <div key={option.value} className="flex items-center">
                          <input
                            type="checkbox"
                            id={`service-${option.value}`}
                            value={option.value}
                            checked={selectedServices.includes(option.value)}
                            onChange={handleServiceChange}
                            className="h-4 w-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500"
                          />
                          <label htmlFor={`service-${option.value}`} className="ml-2 text-sm text-gray-700">
                            {option.label}
                          </label>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-gray-500 text-sm">Please select a business type first to see relevant services</p>
                  )}
                </div>
              </div>
            </div>
            
            {/* Client Needs Section */}
            <div>
              <h2 className="text-lg font-medium text-gray-900 mb-4 pb-2 border-b border-gray-200">Client Needs & Market Research</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-4">
                <div>
                  <label htmlFor="challenges" className="block text-sm font-medium text-gray-700 mb-1">
                    Key Challenges/Goals
                  </label>
                  <textarea
                    id="challenges"
                    name="challenges"
                    value={formData.challenges}
                    onChange={handleInputChange}
                    rows={4}
                    className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                    placeholder="What are the client's main challenges or goals?"
                  ></textarea>
                </div>
                
                <div>
                  <label htmlFor="targetAudience" className="block text-sm font-medium text-gray-700 mb-1">
                    Target Audience
                  </label>
                  <textarea
                    id="targetAudience"
                    name="targetAudience"
                    value={formData.targetAudience}
                    onChange={handleInputChange}
                    rows={4}
                    className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                    placeholder="Describe the client's ideal customers"
                  ></textarea>
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="competitorNames" className="block text-sm font-medium text-gray-700 mb-1">
                    Competitors
                  </label>
                  <input
                    type="text"
                    id="competitorNames"
                    name="competitorNames"
                    value={formData.competitorNames}
                    onChange={handleInputChange}
                    className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                    placeholder="List any known competitors"
                  />
                </div>
                
                <div>
                  <label htmlFor="notes" className="block text-sm font-medium text-gray-700 mb-1">
                    Additional Notes
                  </label>
                  <textarea
                    id="notes"
                    name="notes"
                    value={formData.notes}
                    onChange={handleInputChange}
                    rows={3}
                    className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                    placeholder="Any additional context about this client"
                  ></textarea>
                </div>
              </div>
            </div>
          </div>
          
          <div className="mt-8 flex justify-end">
            <button
              type="button"
              onClick={() => router.back()}
              className="mr-4 px-5 py-2.5 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 focus:outline-none"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isLoading}
              className={`px-5 py-2.5 rounded-md font-medium ${
                isLoading 
                  ? 'bg-gray-400 text-gray-200 cursor-not-allowed' 
                  : 'bg-primary-600 text-white hover:bg-primary-700'
              }`}
            >
              {isLoading ? 'Creating...' : 'Create Proposal'}
            </button>
          </div>
        </form>
      </div>
    </>
  );
}

export default function CreateProposalPage() {
  const router = useRouter();
  
  useEffect(() => {
    const checkAccess = async () => {
      const adminAccess = await isAdmin();
      if (!adminAccess) {
        router.push('/dashboard');
      }
    };

    checkAccess();
  }, [router]);
  
  return (
    <DashboardLayout>
      <CreateProposalContent />
    </DashboardLayout>
  );
} 