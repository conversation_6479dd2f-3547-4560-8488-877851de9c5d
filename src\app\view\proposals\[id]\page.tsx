'use client';

import React, { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import Image from 'next/image';
import { supabaseWithCookies as supabase } from '@/lib/supabase';
import { EnhancedProposal, createDefaultEnhancedProposal } from '@/types/enhanced-proposal-schema';
import { convertToEnhancedProposal } from '@/lib/ai-proposal-helper';

function ProposalViewContent() {
  const params = useParams();
  const proposalId = params.id as string;
  
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [proposal, setProposal] = useState<any>(null);
  const [proposalMetadata, setProposalMetadata] = useState<any>(null);
  const [enhancedData, setEnhancedData] = useState<EnhancedProposal | null>(null);
  const [currentDate] = useState(new Date().toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  }));
  
  // Load proposal data
  useEffect(() => {
    const fetchProposal = async () => {
      try {
        setLoading(true);
        
        // Log a view event first
        await logViewEvent();
        
        // Get the proposal directly from Supabase to avoid API complexity during transition
        const { data, error } = await supabase
          .from('proposals')
          .select('*')
          .eq('id', proposalId)
          .single();
        
        if (error) {
          throw error;
        }
        
        if (!data) {
          throw new Error('No proposal data returned');
        }
        
        console.log('Proposal data:', data);
        
        // Set both the proposal data and metadata
        setProposal(data);
        setProposalMetadata({
          id: data.id,
          reference_number: data.reference_number,
          client_name: data.client_name,
          business_name: data.business_name,
          created_at: data.created_at,
          updated_at: data.updated_at
        });
        
        // Convert to enhanced proposal format if available
        try {
          if (data.proposal_data) {
            // Try to use the enhanced schema if it's already there
            if (data.proposal_data.clientName || data.proposal_data.businessName) {
              setEnhancedData(data.proposal_data as EnhancedProposal);
            } else {
              // Otherwise convert from the old format
              setEnhancedData(convertToEnhancedProposal({
                ...data,
                ...data.proposal_data
              }));
            }
          } else {
            // Create a default enhanced proposal if no data exists
            setEnhancedData(createDefaultEnhancedProposal(
              data.client_name || '',
              data.business_name || '',
              data.business_type || ''
            ));
          }
        } catch (conversionError) {
          console.error('Error converting to enhanced format:', conversionError);
          // Fall back to default
          setEnhancedData(null);
        }
        
        // Update last_viewed_at in the database
        await supabase
          .from('proposals')
          .update({ last_viewed_at: new Date().toISOString() })
          .eq('id', data.id);
          
        // Log view event to events table
        await supabase
          .from('proposal_events')
          .insert({
            proposal_id: proposalId,
            event_type: 'viewed',
            event_data: {
              user_agent: navigator.userAgent,
              timestamp: new Date().toISOString()
            }
          });
      } catch (error: any) {
        console.error('Error fetching proposal:', error);
        setError('Sorry, we couldn\'t find the proposal you\'re looking for.');
      } finally {
        setLoading(false);
      }
    };
    
    fetchProposal();
  }, [proposalId]);
  
  // Log view event
  const logViewEvent = async () => {
    try {
      console.log('Logging view event for proposal:', proposalId);
      // This is a placeholder - the actual logging is done in fetchProposal
    } catch (error) {
      console.error('Error logging view event:', error);
    }
  };
  
  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };
  
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50">
        <div className="text-center">
          <div className="w-16 h-16 border-t-4 border-primary-500 border-solid rounded-full animate-spin mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading proposal...</p>
        </div>
      </div>
    );
  }
  
  if (error || !proposal) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50">
        <div className="max-w-md p-8 bg-white shadow-lg rounded-lg">
          <div className="text-center">
            <svg className="w-16 h-16 text-red-500 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <h1 className="mt-4 text-xl font-bold text-gray-900">Proposal Not Found</h1>
            <p className="mt-2 text-gray-600">
              {error || "Sorry, we couldn't find the proposal you're looking for."}
            </p>
          </div>
        </div>
      </div>
    );
  }
  
  // Extract data from proposal
  const {
    client_name,
    business_name,
    business_type,
    reference_number,
    created_at,
    proposal_data
  } = proposal;
  
  // Debugging
  console.log('Enhanced data:', enhancedData);
  console.log('Proposal data:', proposal_data);
  
  // Use either enhanced data or fall back to legacy format with careful extraction
  const projectTitle = enhancedData?.overview?.projectTitle || `Digital Growth Proposal for ${business_name}`;
  const clientGoals = enhancedData?.clientGoals || { description: proposal_data?.challenges || '' };
  
  // More careful extraction of packages data from all possible locations
  let packages = [];
  if (enhancedData?.packages && enhancedData.packages.length > 0) {
    // Enhanced format - directly use packages array
    packages = enhancedData.packages;
  } else if (proposal_data?.packages && Array.isArray(proposal_data.packages)) {
    // Old format with packages at root of proposal_data
    packages = proposal_data.packages;
  } else if (proposal_data?.sections && Array.isArray(proposal_data.sections)) {
    // Old format with sections array
    const packagesSection = proposal_data.sections.find((s: any) => 
      s.id === 'packages' || s.title?.toLowerCase().includes('package')
    );
    if (packagesSection?.packages && Array.isArray(packagesSection.packages)) {
      packages = packagesSection.packages;
    }
  }
  
  // If we still don't have packages, use a default
  if (!packages || packages.length === 0) {
    packages = [
      {
        name: "Basic \"GetFound\" Package",
        setupFee: "$499",
        monthlyFee: "$100",
        features: [
          "Custom 5-Page Professional Website",
          "Mobile-Responsive Design",
          "Basic SEO Setup & Optimization"
        ]
      },
      {
        name: "Premium \"GetFound\" SEO & Growth Package",
        setupFee: "$2,999",
        monthlyFee: "$150",
        features: [
          "Custom 10+ Page Professional Website",
          "Advanced SEO Strategy & Implementation",
          "Enhanced Lead Generation System",
          "Ongoing Performance Tracking"
        ],
        isRecommended: true
      }
    ];
  }
  
  const nextSteps = enhancedData?.nextSteps || proposal_data?.nextSteps || [];
  
  // Function to convert business type to readable format
  const formatBusinessType = (type: string) => {
    if (!type) return '';
    return type.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ');
  };
  
  return (
    <div className="bg-gray-50 min-h-screen">
      {/* Modern Header */}
      <header className="bg-gradient-to-r from-primary-600 to-primary-800 text-white shadow-lg">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
            <div className="flex items-center">
              <div className="h-12 w-12 relative bg-white rounded-full p-1 mr-4">
                <Image
                  src="/getfound-icon.png"
                  alt="GetFound Logo"
                  fill
                  style={{ objectFit: 'contain' }}
                  className="p-1"
                />
              </div>
              <div>
                <h1 className="text-2xl font-bold">GetFound</h1>
                <p className="text-sm opacity-90">Digital Growth Solutions</p>
              </div>
            </div>
            <div className="mt-4 md:mt-0 text-right">
              <div className="px-4 py-2 bg-white/10 backdrop-blur-sm rounded-lg">
                <p className="text-sm font-medium">Proposal #{reference_number}</p>
                <p className="text-xs">{formatDate(created_at)}</p>
              </div>
            </div>
          </div>
        </div>
      </header>
      
      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Proposal Header */}
        <div className="bg-white shadow-md rounded-xl p-8 mb-8 border-l-4 border-primary-500">
          <div className="flex flex-col md:flex-row justify-between">
            <div>
              <p className="text-sm text-gray-500 mb-1">PREPARED FOR</p>
              <h1 className="text-3xl font-bold text-gray-900 mb-1">
                {business_name}
              </h1>
              <p className="text-xl text-gray-700">
                {client_name} · {formatBusinessType(business_type)}
              </p>
            </div>
            <div className="mt-4 md:mt-0 flex flex-col items-end">
              <p className="text-sm text-gray-500 mb-1">DATE</p>
              <p className="text-lg font-medium">{currentDate}</p>
            </div>
          </div>
          
          <div className="mt-8">
            <h2 className="text-xl font-semibold text-gray-900 mb-3">{projectTitle}</h2>
            <div className="prose max-w-none">
              <p className="text-gray-700">
                {enhancedData?.overview?.introduction || 
                  `Thank you for considering GetFound for your digital growth needs. We specialize in helping local businesses like yours enhance their online presence and attract ideal customers. Our unique approach combines a professionally designed website with our innovative "GetFound" mobile app, allowing you to effortlessly capture and post your completed projects.`
                }
              </p>
              
              {enhancedData?.overview?.summary && (
                <p className="text-gray-700 mt-4">
                  {enhancedData.overview.summary}
                </p>
              )}
            </div>
          </div>
        </div>
        
        {/* Business Challenges */}
        {(clientGoals?.description || (clientGoals?.goals && clientGoals.goals.length > 0)) && (
          <div className="bg-white shadow-md rounded-xl p-8 mb-8">
            <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
              <svg className="w-5 h-5 mr-2 text-primary-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
              </svg>
              {clientGoals.title || "Your Business Goals"}
            </h2>
            
            {clientGoals.description && (
              <div className="prose max-w-none mb-6">
                <p className="text-gray-700 whitespace-pre-line">{clientGoals.description}</p>
              </div>
            )}
            
            {clientGoals.goals && clientGoals.goals.length > 0 && (
              <ul className="space-y-4">
                {clientGoals.goals.map((goal: string, index: number) => (
                  <li key={index} className="flex">
                    <div className="flex-shrink-0 h-6 w-6 rounded-full bg-primary-100 text-primary-600 flex items-center justify-center mr-3 mt-0.5">
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                      </svg>
                    </div>
                    <span className="text-gray-700">{goal}</span>
                  </li>
                ))}
              </ul>
            )}
          </div>
        )}
        
        {/* AI Recommendation Section */}
        {enhancedData?.aiRecommendation && (
          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-xl p-8 mb-8">
            <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
              <svg className="w-5 h-5 mr-2 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
              </svg>
              {enhancedData.aiRecommendation.title}
            </h2>

            <div className="bg-white rounded-lg p-6 shadow-sm">
              <div className="flex items-center justify-between mb-4">
                <div>
                  <h3 className="text-lg font-medium text-gray-900">
                    Recommended: <span className="text-primary-600">{enhancedData.aiRecommendation.recommendedPackage === 'premium' ? 'Premium Package' : 'Basic Package'}</span>
                  </h3>
                  <div className="flex items-center mt-1">
                    <span className="text-sm text-gray-600 mr-2">Confidence:</span>
                    <div className="flex items-center">
                      <div className="w-24 bg-gray-200 rounded-full h-2 mr-2">
                        <div
                          className="bg-green-500 h-2 rounded-full"
                          style={{ width: `${(enhancedData.aiRecommendation.confidence || 0) * 100}%` }}
                        ></div>
                      </div>
                      <span className="text-sm font-medium text-green-600">
                        {Math.round((enhancedData.aiRecommendation.confidence || 0) * 100)}%
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-2">Why we recommend this package:</h4>
                <ul className="space-y-2">
                  {enhancedData.aiRecommendation.reasoning?.map((reason: string, index: number) => (
                    <li key={index} className="flex items-start">
                      <div className="flex-shrink-0 h-5 w-5 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center mr-2 mt-0.5">
                        <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                      </div>
                      <span className="text-gray-700 text-sm">{reason}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        )}

        {/* Package Comparison */}
        <div className="mb-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-6 flex items-center">
            <svg className="w-5 h-5 mr-2 text-primary-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
            </svg>
            Package Options
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {packages.map((pkg: any, index: number) => {
              const isPremium = pkg.isRecommended || pkg.name.toLowerCase().includes('premium');
              return (
                <div 
                  key={index} 
                  className={`rounded-xl overflow-hidden transition-all transform hover:scale-105 hover:shadow-xl ${
                    isPremium 
                      ? 'border-2 border-primary-500 shadow-lg' 
                      : 'border border-gray-200 shadow-md'
                  }`}
                >
                  <div className={`p-6 ${
                    isPremium
                      ? 'bg-gradient-to-r from-primary-600 to-primary-700 text-white'
                      : 'bg-gray-50'
                  }`}>
                    <h3 className={`text-xl font-bold ${isPremium ? 'text-white' : 'text-gray-900'}`}>
                      {pkg.name}
                    </h3>
                    {isPremium && (
                      <span className="inline-block mt-2 px-3 py-1 bg-white text-primary-700 rounded-full text-xs font-medium">
                        RECOMMENDED
                      </span>
                    )}
                    
                    {pkg.description && (
                      <p className={`mt-2 text-sm ${isPremium ? 'text-white/80' : 'text-gray-600'}`}>
                        {pkg.description}
                      </p>
                    )}
                  </div>
                  
                  <div className="p-6 bg-white">
                    <div className="mb-6">
                      <p className="text-3xl font-bold text-gray-900">{pkg.setupFee}</p>
                      <p className="text-gray-600">One-time setup</p>
                      
                      <div className="mt-3 flex items-baseline">
                        <p className="text-xl font-semibold text-gray-900">{pkg.monthlyFee}</p>
                        <span className="text-gray-500 ml-1">/month</span>
                      </div>
                      
                      {pkg.paymentBreakdown?.amortized && (
                        <p className="mt-2 text-sm text-primary-600 bg-primary-50 p-2 rounded">
                          Amortized option: {pkg.paymentBreakdown.amortized}
                        </p>
                      )}
                    </div>
                    
                    <div className="border-t border-gray-100 pt-6">
                      <h4 className="font-medium text-gray-900 mb-4">What's included:</h4>
                      <ul className="space-y-3">
                        {pkg.features && pkg.features.map((feature: string, featureIndex: number) => (
                          <li key={featureIndex} className="flex items-start">
                            <div className={`flex-shrink-0 h-5 w-5 rounded-full ${isPremium ? 'bg-primary-100 text-primary-600' : 'bg-gray-100 text-gray-600'} flex items-center justify-center mr-2 mt-0.5`}>
                              <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                              </svg>
                            </div>
                            <span className="text-gray-700">{feature}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
        
        {/* Payment Options */}
        <div className="bg-white shadow-md rounded-xl p-8 mb-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-6 flex items-center">
            <svg className="w-5 h-5 mr-2 text-primary-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            Flexible Payment Options
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="border border-gray-200 rounded-xl p-6 bg-gray-50 hover:bg-white hover:shadow-md transition-all">
              <h3 className="font-medium text-gray-900 mb-3">Option 1: Standard Payment</h3>
              <p className="text-gray-700 mb-4">
                {enhancedData?.paymentOptions?.standard?.description || 
                  "Pay the setup fee upfront and the monthly subscription fee each month thereafter."}
              </p>
              
              <div className="mt-4 border-t border-gray-200 pt-4">
                <h4 className="text-sm font-medium text-gray-700 mb-2">Payment breakdown:</h4>
                <ul className="space-y-2 text-sm">
                  <li className="flex justify-between">
                    <span>Initial payment:</span>
                    <span className="font-medium">Setup fee</span>
                  </li>
                  <li className="flex justify-between">
                    <span>Monthly thereafter:</span>
                    <span className="font-medium">Monthly subscription</span>
                  </li>
                </ul>
              </div>
            </div>
            
            <div className="border-2 border-primary-500 rounded-xl p-6 bg-white shadow-md relative">
              <div className="absolute -top-3 left-4 px-3 py-1 bg-primary-500 text-white text-xs rounded-full font-medium">
                POPULAR CHOICE
              </div>
              
              <h3 className="font-medium text-gray-900 mb-3">Option 2: Amortized Setup Fee</h3>
              <p className="text-gray-700 mb-4">
                {enhancedData?.paymentOptions?.amortized?.description || 
                  "Spread the setup fee over 12 months, added to your monthly subscription."}
              </p>
              
              <div className="mt-4 border-t border-gray-200 pt-4">
                <h4 className="text-sm font-medium text-gray-700 mb-2">Payment breakdown:</h4>
                <ul className="space-y-2 text-sm">
                  {packages.map((pkg: any, index: number) => (
                    <li key={index} className="flex justify-between py-1 border-b border-gray-100 last:border-0">
                      <span>{pkg.name}:</span>
                      <span className="font-medium">{pkg.paymentBreakdown?.amortized || `Calculated based on package`}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        </div>
        
        {/* Add-on Services */}
        {enhancedData?.addons && enhancedData.addons.items && enhancedData.addons.items.length > 0 && (
          <div className="bg-white shadow-md rounded-xl p-8 mb-8">
            <h2 className="text-xl font-semibold text-gray-900 mb-6 flex items-center">
              <svg className="w-5 h-5 mr-2 text-primary-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v3m0 0v3m0-3h3m-3 0H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
              {enhancedData.addons.title || "Optional Add-on Services"}
            </h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {enhancedData.addons.items.map((addon, index) => (
                <div key={index} className="border border-gray-200 rounded-lg p-5 hover:shadow-md transition-all">
                  <div className="flex justify-between items-start">
                    <h3 className="font-medium text-gray-900">{addon.name}</h3>
                    <span className="bg-primary-100 text-primary-800 text-xs font-medium px-2.5 py-0.5 rounded">
                      {addon.price}
                    </span>
                  </div>
                  
                  <p className="mt-2 text-gray-600 text-sm">{addon.description}</p>
                  
                  {addon.launchDate && (
                    <p className="mt-2 text-xs text-gray-500">
                      Available starting {addon.launchDate}
                    </p>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}
        
        {/* AI-Generated Questions & Answers */}
        {enhancedData?.specificQuestions && enhancedData.specificQuestions.questions && enhancedData.specificQuestions.questions.length > 0 && (
          <div className="bg-white shadow-md rounded-xl p-8 mb-8 border-l-4 border-green-500">
            <h2 className="text-xl font-semibold text-gray-900 mb-6 flex items-center">
              <svg className="w-5 h-5 mr-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
              {enhancedData.specificQuestions.title}
            </h2>

            <div className="space-y-6">
              {enhancedData.specificQuestions.questions.map((qa: any, index: number) => (
                <div key={index} className="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
                  <div className="flex items-start">
                    <div className="flex-shrink-0 h-8 w-8 rounded-full bg-green-100 text-green-600 flex items-center justify-center mr-4 mt-1">
                      <span className="text-sm font-semibold">Q</span>
                    </div>
                    <div className="flex-1">
                      <h3 className="text-lg font-medium text-gray-900 mb-3">{qa.question}</h3>
                      <div className="flex items-start">
                        <div className="flex-shrink-0 h-8 w-8 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center mr-4 mt-1">
                          <span className="text-sm font-semibold">A</span>
                        </div>
                        <div className="flex-1">
                          <p className="text-gray-700 leading-relaxed">{qa.answer}</p>
                          {qa.category && (
                            <span className="inline-block mt-3 px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full">
                              {qa.category.charAt(0).toUpperCase() + qa.category.slice(1)}
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Custom Sections */}
        {enhancedData?.customSections && enhancedData.customSections.length > 0 &&
          enhancedData.customSections.map((section, index) => (
            <div key={index} className="bg-white shadow-md rounded-xl p-8 mb-8">
              <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                <svg className="w-5 h-5 mr-2 text-primary-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                {section.title}
              </h2>
              <div className="prose max-w-none">
                <p className="text-gray-700 whitespace-pre-line">{section.content}</p>
              </div>
            </div>
          ))
        }
        
        {/* Next Steps */}
        <div className="bg-white shadow-md rounded-xl p-8 mb-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-6 flex items-center">
            <svg className="w-5 h-5 mr-2 text-primary-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 5l7 7-7 7M5 5l7 7-7 7"></path>
            </svg>
            Next Steps
          </h2>
          
          <ol className="space-y-6">
            {nextSteps.map((step: string, index: number) => (
              <li key={index} className="flex">
                <div className="flex-shrink-0 h-8 w-8 rounded-full bg-primary-500 text-white flex items-center justify-center mr-4 font-semibold">
                  {index + 1}
                </div>
                <div className="pt-1">
                  <p className="text-gray-700">{step}</p>
                </div>
              </li>
            ))}
          </ol>
        </div>
        
        {/* CTA Section */}
        <div className="bg-gradient-to-r from-primary-600 to-primary-800 text-white rounded-xl p-8 mb-8 shadow-lg">
          <div className="text-center max-w-3xl mx-auto">
            <h2 className="text-2xl font-bold mb-4">Ready to Get Started?</h2>
            <p className="mb-6">
              {enhancedData?.contactInfo?.ctaText || 
                "Contact us today to discuss this proposal and start building your enhanced online presence."}
            </p>
            <div className="inline-block bg-white text-primary-700 font-medium px-6 py-3 rounded-lg shadow hover:bg-gray-100 transition-colors">
              Email: <EMAIL>
            </div>
            <p className="mt-4 text-sm opacity-90">
              We'll respond within 24 hours to answer any questions and help you move forward.
            </p>
          </div>
        </div>
      </main>
      
      {/* Footer */}
      <footer className="bg-gray-800 text-white py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="flex items-center mb-4 md:mb-0">
              <div className="h-10 w-10 relative bg-white rounded-full p-1 mr-3">
                <Image
                  src="/getfound-icon.png"
                  alt="GetFound Logo"
                  fill
                  style={{ objectFit: 'contain' }}
                  className="p-1"
                />
              </div>
              <div>
                <h3 className="text-lg font-semibold">GetFound</h3>
                <p className="text-xs text-gray-400">Digital Growth Solutions</p>
              </div>
            </div>
            <div className="text-center md:text-right">
              <p className="text-sm text-gray-400">
                &copy; {new Date().getFullYear()} GetFound. All rights reserved.
              </p>
              <p className="text-xs text-gray-500 mt-1">
                Proposal #{reference_number} | Created {formatDate(created_at)}
              </p>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}

// This wrapper ensures the ProposalViewContent is only rendered on the client
export default function ProposalViewPage() {
  return (
    <div>
      <ProposalViewContent />
    </div>
  );
} 