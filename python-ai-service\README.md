# GetFound AI Proposal Service

A Pydantic AI-powered service for dynamic proposal generation that works with ANY business type.

## 🎯 Overview

This service provides intelligent proposal generation using Pydantic AI, featuring:

- **Universal Business Support**: Works for contractors, dentists, lawyers, restaurants, etc.
- **Structured Output**: Uses Pydantic models for consistent, type-safe responses
- **Question Extraction**: Automatically identifies and answers customer questions
- **Package Recommendations**: Intelligently recommends Basic vs Premium packages
- **Logfire Integration**: Comprehensive monitoring and tracing

## 🚀 Quick Start

### 1. Setup Environment

```bash
# Run the setup script
python setup.py

# Activate virtual environment
source venv/bin/activate  # Linux/macOS
# or
venv\Scripts\activate     # Windows

# Install dependencies
pip install -r requirements.txt
```

### 2. Configure Environment Variables

Edit `.env` file and add your API keys:

```env
OPENAI_API_KEY=your_openai_api_key_here
LOGFIRE_TOKEN=your_logfire_token_here  # Optional
```

### 3. Start the Service

```bash
# Using the startup script (recommended)
python start.py

# Or directly with uvicorn
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

The service will be available at:
- **API**: http://localhost:8000
- **Documentation**: http://localhost:8000/docs
- **Health Check**: http://localhost:8000/

## 📋 API Endpoints

### POST /generate-proposal

Generate a complete proposal from customer input.

**Request:**
```json
{
  "customer_input": "1.) Website - current website (click funnel)...",
  "business_name": "Lowery Finishing",  // Optional
  "business_type": "contractor"         // Optional
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "project_title": "Modern Website & Digital Marketing Solution for Lowery Finishing",
    "acknowledgment": "Thank you for discussing your vision...",
    "package_recommendation": {
      "recommended": "premium",
      "confidence": 0.85,
      "reasoning": ["Multiple target markets", "Established business"],
      "why_this_package": ["Advanced SEO features", "Dedicated service pages"]
    },
    "questions_and_answers": [
      {
        "question": "Should I ask builders for Google reviews?",
        "answer": "Yes, absolutely! Here's how...",
        "category": "google"
      }
    ],
    "next_steps": ["Schedule discovery call", "Review premium features"]
  }
}
```

### POST /analyze

Quick customer analysis for preview purposes.

**Request:**
```json
{
  "customer_input": "I have a ClickFunnels site but want something more professional..."
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "business_name": "Your Business",
    "current_situation": "Currently using ClickFunnels but wants a professional website",
    "key_needs": ["Professional website presence", "Easier quote process"],
    "business_maturity": "growing"
  }
}
```

### GET /test

Test endpoint with sample Lowery Finishing data.

## 🏗️ Architecture

### Pydantic Models

```python
class CustomerAnalysis(BaseModel):
    business_name: str
    current_situation: str
    key_needs: List[str]
    business_maturity: Literal["startup", "growing", "established"]

class PackageRecommendation(BaseModel):
    recommended: Literal["basic", "premium"]
    confidence: float
    reasoning: List[str]
    why_this_package: List[str]

class QuestionAnswer(BaseModel):
    question: str
    answer: str
    category: Literal["website", "google", "goals", "pricing", "process", "general"]

class ProposalContent(BaseModel):
    project_title: str
    acknowledgment: str
    package_recommendation: PackageRecommendation
    questions_and_answers: List[QuestionAnswer]
    next_steps: List[str]
```

### AI Agent Configuration

The Pydantic AI agent is configured with:
- **Model**: GPT-4o for best reasoning capabilities
- **System Prompt**: Comprehensive instructions for proposal generation
- **Result Type**: Structured `ProposalContent` model
- **Logfire Integration**: Automatic tracing and monitoring

## 🔧 Integration with Next.js

The TypeScript service (`src/lib/pydantic-ai-service.ts`) provides:

```typescript
// Generate complete proposal
const proposal = await pydanticAIService.generateProposal(customerInput);

// Quick analysis
const analysis = await pydanticAIService.analyzeCustomer(customerInput);
```

## 📊 Monitoring with Logfire

The service includes comprehensive Logfire integration:

- **Request Tracing**: Track all API calls and processing time
- **AI Agent Monitoring**: Monitor AI decision-making process
- **Error Tracking**: Capture and analyze failures
- **Performance Metrics**: Response times and success rates

## 🧪 Testing

### Test with Sample Data

```bash
# Test the service
curl -X GET http://localhost:8000/test

# Test proposal generation
curl -X POST http://localhost:8000/generate-proposal \
  -H "Content-Type: application/json" \
  -d '{"customer_input": "I have a ClickFunnels site but want something professional..."}'
```

### Expected Output

The service should extract questions like:
- "Should I ask builders for Google reviews?"
- "How can I fix the automatic photo issue on my Google listing?"
- "How will you help me focus on exterior door swaps specifically?"

And recommend the appropriate package based on business maturity and needs.

## 🔒 Security

- **CORS Configuration**: Restricted to Next.js development servers
- **Input Validation**: Pydantic models ensure data integrity
- **API Key Protection**: Environment variables for sensitive data
- **Error Handling**: Graceful failure with informative messages

## 🚨 Troubleshooting

### Common Issues

1. **Import Errors**: Make sure virtual environment is activated and dependencies installed
2. **API Key Missing**: Check `.env` file has `OPENAI_API_KEY`
3. **Port Conflicts**: Service runs on port 8000 by default
4. **CORS Errors**: Ensure Next.js is running on allowed origins

### Debug Mode

Set environment variable for detailed logging:
```bash
export LOG_LEVEL=debug
python start.py
```

## 📈 Performance

- **Response Time**: Typically 2-5 seconds for proposal generation
- **Concurrency**: Supports multiple simultaneous requests
- **Caching**: Consider implementing Redis for frequently requested proposals
- **Rate Limiting**: Implement if needed for production use

## 🔄 Development

### Adding New Question Categories

1. Update the `QuestionAnswer` model category enum
2. Enhance the AI system prompt with new category examples
3. Test with relevant customer input

### Improving Package Recommendations

1. Modify the system prompt logic
2. Add new reasoning factors
3. Test with various business scenarios

## 📝 License

Part of the GetFound proposal system.
