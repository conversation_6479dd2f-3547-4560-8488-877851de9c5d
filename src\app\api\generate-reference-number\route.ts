import { NextRequest, NextResponse } from 'next/server';
import { generateProposalReferenceNumber } from '@/lib/reference-number-generator';

export async function POST(request: NextRequest) {
  try {
    const referenceNumber = await generateProposalReferenceNumber();
    
    return NextResponse.json({
      success: true,
      referenceNumber: referenceNumber
    });
    
  } catch (error: any) {
    console.error('Error generating reference number:', error);
    
    // Fallback to timestamp-based reference
    const fallbackReference = `GF-${new Date().getFullYear()}-${Date.now().toString().slice(-4)}`;
    
    return NextResponse.json({
      success: false,
      error: 'Failed to generate sequential reference number',
      referenceNumber: fallbackReference,
      message: error.message
    }, { status: 500 });
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Reference Number Generator API',
    format: 'GF-YYYY-NNNN',
    example: 'GF-2025-0001'
  });
}
