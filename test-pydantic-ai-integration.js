/**
 * Test script for Pydantic AI integration
 * Tests the complete flow from customer input to proposal generation
 */

const fetch = require('node-fetch');

// Test data - Lowery Finishing example
const testCustomerInput = `
1.) Website
  - current website (click funnel) https://www.loweryfinishing.com/optin-604655891694725683997
  - I would like to have a legit website that would make it even easier for customers to request quotes. I also feel it could be designed better to find the work that I want to do and not be so broad. I'll explain the work I want to find further in this email.

2.) Google listing 
  - I have a few good reviews and try to get them each time we do a job for a customer. Usually we are doing work for builders and don't ask for a review. But should we? Is there a better way to show credibility with big builders?
  - One of the pictures from a review automatically shows up with the listing which is kind of annoying...Not sure how to fix this.

3.) Goals
  A. Homeowners
      - exterior door swaps. Not repairs, or anything else. Just straight swaps. This is one I would like to highlight in particular. Good money maker for us. I would also like to charge a fee to bid if we need to come out to take measurements. Would be nice to have a system with instructions on how to give us information so we can give them an easy free estimate without driving to the job.
      - Everything else listed under (B) as well.
 
 B.) Builders (apartments, custom homes, track homes)
      We Install the following below:
Exterior Doors, Interior Doors, Pocket doors, Bi Fold Doors, Bi Pass Doors, Barndoor, Transoms, Cased Opening, Window surrounds, Window sills, Window mulls, Closet Shelf, Closet Shelf face framed, Closet Linen, Closet Linen face framed, Base, Base Shoe, Treads & Risers, Skirt Boards, Wall cap, Handrail, Door and bath hardware, Attic access doors, Open Rail, Newel Post, Deco Shelf, Mantel, Built in bookcases, Built in bench, Wainscot, built in desk / sub top for counter, built in Lockers, Setting Exterior Doors, Setting Pocket Door frames
`;

async function testPythonService() {
  console.log('🧪 Testing Python AI Service...');
  
  const pythonServiceUrl = 'http://localhost:8000';
  
  try {
    // Test 1: Health check
    console.log('\n1️⃣ Testing health check...');
    const healthResponse = await fetch(`${pythonServiceUrl}/`);
    const healthData = await healthResponse.json();
    console.log('✅ Health check:', healthData.status);
    
    // Test 2: Customer analysis
    console.log('\n2️⃣ Testing customer analysis...');
    const analysisResponse = await fetch(`${pythonServiceUrl}/analyze`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ customer_input: testCustomerInput })
    });
    
    if (!analysisResponse.ok) {
      throw new Error(`Analysis failed: ${analysisResponse.status}`);
    }
    
    const analysisData = await analysisResponse.json();
    console.log('✅ Analysis result:');
    console.log(`   Business: ${analysisData.data.business_name}`);
    console.log(`   Situation: ${analysisData.data.current_situation}`);
    console.log(`   Maturity: ${analysisData.data.business_maturity}`);
    console.log(`   Needs: ${analysisData.data.key_needs.join(', ')}`);
    
    // Test 3: Proposal generation
    console.log('\n3️⃣ Testing proposal generation...');
    const proposalResponse = await fetch(`${pythonServiceUrl}/generate-proposal`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ 
        customer_input: testCustomerInput,
        business_name: 'Lowery Finishing',
        business_type: 'contractor'
      })
    });
    
    if (!proposalResponse.ok) {
      throw new Error(`Proposal generation failed: ${proposalResponse.status}`);
    }
    
    const proposalData = await proposalResponse.json();
    console.log('✅ Proposal generated:');
    console.log(`   Title: ${proposalData.data.project_title}`);
    console.log(`   Package: ${proposalData.data.package_recommendation.recommended}`);
    console.log(`   Confidence: ${proposalData.data.package_recommendation.confidence}`);
    console.log(`   Questions: ${proposalData.data.questions_and_answers.length}`);
    
    // Show extracted questions
    console.log('\n📋 Extracted Questions:');
    proposalData.data.questions_and_answers.forEach((qa, index) => {
      console.log(`   ${index + 1}. ${qa.question} (${qa.category})`);
    });
    
    return true;
    
  } catch (error) {
    console.error('❌ Python service test failed:', error.message);
    return false;
  }
}

async function testNextJSAPI() {
  console.log('\n🧪 Testing Next.js API Integration...');
  
  const nextjsApiUrl = 'http://localhost:3000/api/generate-dynamic-proposal';
  
  try {
    // Test GET endpoint (test mode)
    console.log('\n1️⃣ Testing GET endpoint...');
    const getResponse = await fetch(nextjsApiUrl);
    
    if (!getResponse.ok) {
      throw new Error(`GET test failed: ${getResponse.status}`);
    }
    
    const getData = await getResponse.json();
    console.log('✅ GET test result:');
    console.log(`   Business: ${getData.sampleAnalysis.businessName}`);
    console.log(`   Questions: ${getData.proposalData.questionsFound}`);
    
    // Test POST endpoint
    console.log('\n2️⃣ Testing POST endpoint...');
    const postResponse = await fetch(nextjsApiUrl, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ 
        customerInfo: testCustomerInput,
        businessName: 'Lowery Finishing',
        businessType: 'contractor'
      })
    });
    
    if (!postResponse.ok) {
      throw new Error(`POST test failed: ${postResponse.status}`);
    }
    
    const postData = await postResponse.json();
    console.log('✅ POST test result:');
    console.log(`   Success: ${postData.success}`);
    console.log(`   Package: ${postData.recommendations.package}`);
    console.log(`   Questions: ${postData.questionsAnswered}`);
    
    return true;
    
  } catch (error) {
    console.error('❌ Next.js API test failed:', error.message);
    return false;
  }
}

async function runAllTests() {
  console.log('🚀 GetFound AI Integration Tests');
  console.log('=' * 50);
  
  let pythonServiceOk = false;
  let nextjsApiOk = false;
  
  // Test Python service
  pythonServiceOk = await testPythonService();
  
  // Test Next.js API (only if Python service is working)
  if (pythonServiceOk) {
    nextjsApiOk = await testNextJSAPI();
  } else {
    console.log('\n⚠️  Skipping Next.js API tests (Python service not available)');
  }
  
  // Summary
  console.log('\n' + '=' * 50);
  console.log('📊 Test Results:');
  console.log(`   Python AI Service: ${pythonServiceOk ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`   Next.js API: ${nextjsApiOk ? '✅ PASS' : '❌ FAIL'}`);
  
  if (pythonServiceOk && nextjsApiOk) {
    console.log('\n🎉 All tests passed! Integration is working correctly.');
  } else {
    console.log('\n🔧 Some tests failed. Check the setup:');
    if (!pythonServiceOk) {
      console.log('   - Make sure Python AI service is running on port 8000');
      console.log('   - Check OpenAI API key is configured');
      console.log('   - Verify all Python dependencies are installed');
    }
    if (!nextjsApiOk) {
      console.log('   - Make sure Next.js development server is running on port 3000');
      console.log('   - Check TypeScript compilation is successful');
    }
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  runAllTests().catch(console.error);
}

module.exports = {
  testPythonService,
  testNextJSAPI,
  runAllTests
};
