"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from mistralai.types import BaseModel
from typing import Literal, Optional
from typing_extensions import NotRequired, TypedDict


UnarchiveFTModelOutObject = Literal["model"]


class UnarchiveFTModelOutTypedDict(TypedDict):
    id: str
    object: NotRequired[UnarchiveFTModelOutObject]
    archived: NotRequired[bool]


class UnarchiveFTModelOut(BaseModel):
    id: str

    object: Optional[UnarchiveFTModelOutObject] = "model"

    archived: Optional[bool] = False
