'use client';

import React, { useState, useRef, useCallback, useEffect } from 'react';

interface VoiceInputProps {
  onTranscription: (text: string) => void;
  onTranscriptionUpdate: (text: string) => void;
  disabled?: boolean;
  className?: string;
}

const VoiceInput: React.FC<VoiceInputProps> = ({
  onTranscription,
  onTranscriptionUpdate,
  disabled = false,
  className = ''
}) => {
  const [isRecording, setIsRecording] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentTranscription, setCurrentTranscription] = useState('');

  const websocketRef = useRef<WebSocket | null>(null);
  const streamRef = useRef<MediaStream | null>(null);
  const audioContextRef = useRef<AudioContext | null>(null);
  const processorRef = useRef<ScriptProcessorNode | null>(null);

  // Cleanup function
  const cleanup = useCallback(() => {
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop());
      streamRef.current = null;
    }

    if (websocketRef.current) {
      if (websocketRef.current.readyState === WebSocket.OPEN) {
        // Send termination message for AssemblyAI v3
        websocketRef.current.send(JSON.stringify({ type: "Terminate" }));
      }
      websocketRef.current.close();
      websocketRef.current = null;
    }

    if (processorRef.current) {
      processorRef.current.disconnect();
      processorRef.current = null;
    }

    if (audioContextRef.current) {
      audioContextRef.current.close();
      audioContextRef.current = null;
    }

    setIsRecording(false);
    setIsConnecting(false);
    setCurrentTranscription('');
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return cleanup;
  }, [cleanup]);

  // Convert Float32Array to Int16Array for AssemblyAI
  const float32ToInt16 = (float32Array: Float32Array): Int16Array => {
    const int16Array = new Int16Array(float32Array.length);
    for (let i = 0; i < float32Array.length; i++) {
      const sample = Math.max(-1, Math.min(1, float32Array[i]));
      int16Array[i] = sample < 0 ? sample * 0x8000 : sample * 0x7FFF;
    }
    return int16Array;
  };

  const startRecording = async () => {
    try {
      setError(null);
      setIsConnecting(true);
      setCurrentTranscription('');

      // Get connection info from our proxy endpoint
      const proxyResponse = await fetch('/api/assemblyai-proxy');
      if (!proxyResponse.ok) {
        throw new Error('Failed to get AssemblyAI connection info');
      }
      const { token, websocketUrl, sampleRate, apiVersion } = await proxyResponse.json();
      console.log('Got AssemblyAI connection info:', { apiVersion, sampleRate });

      // Request microphone access
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          sampleRate: sampleRate,
          channelCount: 1,
          echoCancellation: true,
          noiseSuppression: true,
        }
      });

      streamRef.current = stream;

      // Create WebSocket connection to AssemblyAI API
      // Using token-based authentication which works in browsers
      const websocket = new WebSocket(websocketUrl);
      console.log('Connecting to AssemblyAI:', websocketUrl);

      websocketRef.current = websocket;

      websocket.onopen = () => {
        console.log('AssemblyAI WebSocket connected');
        setIsConnecting(false);
        setIsRecording(true);
      };

      websocket.onmessage = (event) => {
        try {
          console.log('WebSocket message received:', event.data);

          // Handle real AssemblyAI v2 messages (paid account)
          if (apiVersion === 'v2') {
            const data = JSON.parse(event.data);
            console.log('AssemblyAI v2 message:', data);

            if (data.message_type === 'SessionBegins') {
              console.log('AssemblyAI session started:', data.session_id);
            } else if (data.message_type === 'PartialTranscript') {
              const transcriptText = data.text || '';
              if (transcriptText.trim()) {
                // Partial transcript - show preview
                const previewText = currentTranscription + (currentTranscription ? ' ' : '') + transcriptText;
                onTranscriptionUpdate(previewText);
              }
            } else if (data.message_type === 'FinalTranscript') {
              const transcriptText = data.text || '';
              if (transcriptText.trim()) {
                // Final transcript - add to the input
                setCurrentTranscription(prev => {
                  const newText = prev + (prev ? ' ' : '') + transcriptText;
                  onTranscription(newText);
                  return newText;
                });
              }
            } else if (data.message_type === 'SessionTerminated') {
              console.log('AssemblyAI session terminated');
            }
            return;
          }

          // Handle demo mode with simulated transcription (fallback)
          if (apiVersion === 'demo') {
            // Simulate real-time transcription for demo
            setTimeout(() => {
              const demoTexts = [
                'Hello, I would like to know more about your SEO services',
                'Can you help me improve my website ranking?',
                'What packages do you offer for small businesses?',
                'I need help with local search optimization',
                'How much does your basic package cost?'
              ];
              const randomText = demoTexts[Math.floor(Math.random() * demoTexts.length)];

              // Simulate partial transcript first
              onTranscriptionUpdate(randomText);

              // Then final transcript after a short delay
              setTimeout(() => {
                setCurrentTranscription(prev => {
                  const newText = prev + (prev ? ' ' : '') + randomText;
                  onTranscription(newText);
                  return newText;
                });
              }, 500);
            }, 1000);
            return;
          }
        } catch (error) {
          console.error('Error parsing WebSocket message:', error);
        }
      };

      websocket.onerror = (error) => {
        console.error('WebSocket error:', error);
        setError('Connection error. Please try again.');
        cleanup();
      };

      websocket.onclose = () => {
        console.log('AssemblyAI WebSocket closed');
        setIsRecording(false);
        setIsConnecting(false);
      };

      // Set up audio processing
      const audioContext = new AudioContext({ sampleRate: 16000 });
      audioContextRef.current = audioContext;
      
      const source = audioContext.createMediaStreamSource(stream);
      const processor = audioContext.createScriptProcessor(4096, 1, 1);
      processorRef.current = processor;

      processor.onaudioprocess = (event) => {
        if (websocket.readyState === WebSocket.OPEN) {
          const inputData = event.inputBuffer.getChannelData(0);
          const int16Data = float32ToInt16(inputData);

          // Convert to base64 for AssemblyAI v2 API
          const uint8Array = new Uint8Array(int16Data.buffer);
          const base64Audio = btoa(String.fromCharCode.apply(null, Array.from(uint8Array)));

          // Send base64 encoded audio data
          websocket.send(JSON.stringify({
            audio_data: base64Audio
          }));
        }
      };

      source.connect(processor);
      processor.connect(audioContext.destination);

    } catch (error: any) {
      console.error('Error starting recording:', error);
      setError(error.message || 'Could not access microphone. Please check permissions.');
      setIsConnecting(false);
      cleanup();
    }
  };

  const stopRecording = () => {
    cleanup();
  };

  const toggleRecording = () => {
    if (isRecording || isConnecting) {
      stopRecording();
    } else {
      startRecording();
    }
  };

  const getButtonClasses = () => {
    const baseClasses = "p-2 rounded-full transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2";
    
    if (disabled) {
      return `${baseClasses} bg-gray-100 text-gray-400 cursor-not-allowed`;
    }
    
    if (isRecording) {
      return `${baseClasses} bg-red-500 text-white hover:bg-red-600 focus:ring-red-500 animate-pulse`;
    }
    
    if (isConnecting) {
      return `${baseClasses} bg-yellow-500 text-white focus:ring-yellow-500`;
    }
    
    return `${baseClasses} bg-gray-200 text-gray-600 hover:bg-gray-300 focus:ring-gray-500`;
  };

  const getIconClasses = () => {
    return `w-5 h-5 ${isRecording ? 'animate-pulse' : ''}`;
  };

  return (
    <div className={`relative ${className}`}>
      <button
        type="button"
        onClick={toggleRecording}
        disabled={disabled}
        className={getButtonClasses()}
        title={
          isRecording 
            ? "Stop recording" 
            : isConnecting 
            ? "Connecting..." 
            : "Start voice input"
        }
      >
        {isConnecting ? (
          <svg className={getIconClasses()} fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
        ) : isRecording ? (
          <svg className={getIconClasses()} fill="currentColor" viewBox="0 0 24 24">
            <path d="M6 6h12v12H6z" />
          </svg>
        ) : (
          <svg className={getIconClasses()} fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
          </svg>
        )}
      </button>
      
      {error && (
        <div className="absolute top-full left-0 mt-2 p-2 bg-red-100 border border-red-300 rounded-md text-red-700 text-sm whitespace-nowrap z-10">
          {error}
        </div>
      )}
      
      {isRecording && (
        <div className="absolute top-full left-0 mt-2 p-2 bg-green-100 border border-green-300 rounded-md text-green-700 text-sm whitespace-nowrap z-10">
          Recording... Speak now
        </div>
      )}
    </div>
  );
};

export default VoiceInput;
