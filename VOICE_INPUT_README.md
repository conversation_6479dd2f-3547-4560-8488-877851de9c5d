# Voice Input Integration with AssemblyAI

## Overview
This implementation adds real-time speech-to-text transcription to the AI chat component in the proposal system using AssemblyAI's streaming API.

## Features
- **Dual Input Mode**: Users can type or speak their questions to the AI assistant
- **Real-time Transcription**: Speech is converted to text in real-time as the user speaks
- **Visual Feedback**: Clear indicators show recording state and transcription preview
- **Browser Compatibility**: Works in modern browsers with microphone access
- **Error Handling**: Graceful handling of microphone permissions and connection issues

## Implementation Details

### Components Added
1. **VoiceInput Component** (`src/components/ai/VoiceInput.tsx`)
   - Handles microphone access and audio processing
   - Manages WebSocket connection to AssemblyAI
   - Converts audio to the required format (16kHz, mono, Int16)
   - Provides visual feedback for recording state

2. **Enhanced ProposalChatAssistant** (`src/components/ai/ProposalChatAssistant.tsx`)
   - Integrated voice input alongside text input
   - Shows real-time transcription preview
   - Maintains all existing chat functionality

3. **Updated Proposal View** (`src/app/view/proposals/[id]/page.tsx`)
   - Added AI chat toggle button in header
   - Implemented split-screen layout when chat is active
   - Responsive design for different screen sizes

### Technical Implementation

#### AssemblyAI Integration
- Uses WebSocket connection for real-time streaming
- API Key: `e418098cc7d44675804de163364909b0`
- Sample Rate: 16kHz (required by AssemblyAI)
- Audio Format: Mono, Int16 PCM

#### Audio Processing
- Uses Web Audio API for audio capture and processing
- Converts Float32 audio to Int16 format required by AssemblyAI
- Handles browser microphone permissions
- Processes audio in 4096-sample chunks

#### User Experience
- Microphone button appears next to text input
- Visual indicators for recording state (pulsing animation)
- Real-time transcription preview above input field
- Seamless switching between typing and voice input
- Error messages for permission or connection issues

## Usage

### For Users
1. Click the "AI Assistant" button in the proposal header to open chat
2. In the chat input area, click the microphone icon
3. Grant microphone permissions when prompted
4. Speak your question or request
5. See real-time transcription appear in the input field
6. Edit the transcribed text if needed
7. Press Enter or click Send to submit

### For Developers
```typescript
// Voice input component usage
<VoiceInput
  onTranscription={(text) => setInputValue(text)}
  onTranscriptionUpdate={(text) => setPreviewText(text)}
  disabled={isProcessing}
/>
```

## Browser Requirements
- Modern browser with WebRTC support
- Microphone access permissions
- HTTPS connection (required for microphone access)
- WebSocket support

## Error Handling
- Microphone permission denied
- Network connection issues
- AssemblyAI service unavailable
- Audio processing errors
- WebSocket connection failures

## Security Considerations
- API key is exposed in client-side code (consider server-side proxy for production)
- Microphone permissions are requested only when needed
- Audio data is streamed directly to AssemblyAI (not stored locally)
- WebSocket connections are properly cleaned up

## Future Enhancements
- Server-side API key management
- Support for multiple languages
- Voice activity detection
- Audio quality indicators
- Offline transcription fallback
- Custom wake words

## Testing
- Test API endpoint: `/api/test-voice`
- Browser console logs for debugging
- Network tab to monitor WebSocket connections
- Microphone permissions in browser settings

## Dependencies Added
- `assemblyai`: ^4.8.0 - Official AssemblyAI SDK for streaming transcription
