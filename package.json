{"name": "getfoundapp", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build --no-lint", "start": "next start", "schema:refresh": "node scripts/supabase-introspect.js", "lint": "next lint", "setup:env": "node scripts/setup-environment.js"}, "dependencies": {"@hookform/resolvers": "^4.1.2", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/supabase-js": "^2.49.1", "@types/node": "^20.0.0", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "assemblyai": "^4.13.1", "autoprefixer": "^10.4.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dotenv": "^16.4.7", "nanoid": "^5.1.5", "next": "^14.0.0", "openai": "^5.0.2", "postcss": "^8.4.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.54.2", "recharts": "^2.12.3", "tailwind-merge": "^3.0.2", "tailwindcss": "^3.3.0", "typescript": "^5.0.0", "zod": "^3.24.2"}}