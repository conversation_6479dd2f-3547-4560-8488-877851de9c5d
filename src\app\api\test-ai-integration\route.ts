import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { PydanticAIProposalService } from '@/lib/pydantic-ai-service';

export async function POST(request: NextRequest) {
  try {
    const cookieStore = cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });
    
    const body = await request.json();
    const { customerInfo, businessName, businessType } = body;
    
    console.log('🧪 Testing complete AI integration...');
    
    // Step 1: Test Pydantic AI Service
    const pydanticAI = new PydanticAIProposalService();
    
    console.log('📝 Generating AI proposal...');
    const aiResult = await pydanticAI.generateProposal(customerInfo || `
      Business: ${businessName}
      Type: ${businessType}
      
      We need a professional website to replace our current ClickFunnels setup.
      We want to make it easier for customers to request quotes.
      We also need help with our Google Business Profile and reviews.
    `);
    
    console.log('✅ AI proposal generated successfully');
    
    // Step 2: Create enhanced proposal structure
    const enhancedProposal = {
      clientName: businessName?.split(' ')[0] || 'Client',
      businessName: businessName || 'Test Business',
      businessType: businessType || 'local business',
      overview: {
        projectTitle: aiResult.fullDynamicContent?.projectTitle || `Digital Growth Proposal for ${businessName}`,
        introduction: aiResult.fullDynamicContent?.acknowledgment || 'Thank you for considering GetFound for your digital growth needs.',
        summary: aiResult.sampleAnalysis?.situation || 'Professional website and digital marketing solution'
      },
      clientGoals: {
        title: "Understanding Your Goals",
        description: "Based on our discussion, we've identified your key objectives",
        goals: aiResult.sampleAnalysis?.needs || ['Professional website', 'Better online presence', 'Increased customer engagement']
      },
      packages: [
        {
          name: "Basic \"GetFound\" Package",
          setupFee: "$499",
          monthlyFee: "$100",
          features: [
            "Custom 5-Page Professional Website",
            "GetFound Mobile App Integration",
            "Filterable Service Portfolio",
            "Mobile-Responsive Design",
            "Basic SEO Setup & Optimization",
            "Standard Quote Request Forms",
            "Website Hosting & Maintenance",
            "Google Business Profile Optimization"
          ],
          isRecommended: aiResult.proposalData?.packageRecommendation?.recommended === 'basic'
        },
        {
          name: "Premium \"GetFound\" SEO & Growth Package",
          setupFee: "$2,999",
          monthlyFee: "$150",
          features: [
            "Everything in Basic Package",
            "Expanded Website (10-15+ Pages)",
            "Dedicated Service Pages for Each Offering",
            "Advanced SEO Strategy & Keyword Research",
            "Smart AI Content Builder & Placement",
            "Enhanced Lead Generation System",
            "Comprehensive Google Business Profile Management",
            "Local SEO Optimization for Your Service Area",
            "Keyword Rank Tracking Dashboard Access",
            "Ongoing SEO Reports & Performance Tracking"
          ],
          isRecommended: aiResult.proposalData?.packageRecommendation?.recommended === 'premium'
        }
      ],
      specificQuestions: {
        title: "Addressing Your Specific Questions",
        questions: aiResult.fullDynamicContent?.questionsAndAnswers?.map(qa => ({
          question: qa.question,
          answer: qa.answer,
          category: qa.category
        })) || []
      },
      aiRecommendation: {
        recommendedPackage: aiResult.proposalData?.packageRecommendation?.recommended || 'premium',
        confidence: aiResult.proposalData?.packageRecommendation?.confidence || 0.8,
        reasoning: aiResult.proposalData?.packageRecommendation?.whyThisPackage || ['Advanced features recommended for your business needs'],
        title: "Our Recommendation Based on Your Needs"
      },
      nextSteps: aiResult.fullDynamicContent?.nextSteps || [
        "Schedule a consultation to discuss your specific needs",
        "Select the package that aligns with your business goals",
        "Begin implementation with our specialized team",
        "Launch your enhanced online presence"
      ],
      aiAnalysis: {
        businessName: aiResult.sampleAnalysis?.businessName || businessName,
        currentSituation: aiResult.sampleAnalysis?.situation || 'Seeking professional website solution',
        keyNeeds: aiResult.sampleAnalysis?.needs || ['Professional website', 'Better online presence'],
        businessMaturity: aiResult.sampleAnalysis?.maturity || 'established'
      }
    };
    
    console.log('📊 Enhanced proposal structure created');
    
    // Step 3: Save to Supabase (optional - for testing)
    let proposalId = null;
    if (body.saveToDatabase) {
      const { data: proposalData, error: saveError } = await supabase
        .from('proposals')
        .insert({
          client_name: enhancedProposal.clientName,
          business_name: enhancedProposal.businessName,
          business_type: enhancedProposal.businessType,
          proposal_data: enhancedProposal,
          status: 'draft',
          reference_number: `TEST-${Date.now()}`
        })
        .select()
        .single();
      
      if (saveError) {
        console.error('❌ Error saving to database:', saveError);
      } else {
        proposalId = proposalData.id;
        console.log('💾 Proposal saved to database:', proposalId);
      }
    }
    
    return NextResponse.json({
      success: true,
      message: 'Complete AI integration test successful',
      data: {
        aiResult: aiResult,
        enhancedProposal: enhancedProposal,
        proposalId: proposalId,
        testResults: {
          pydanticAIWorking: !!aiResult.success,
          questionsExtracted: aiResult.proposalData?.questionsFound || 0,
          packageRecommended: aiResult.proposalData?.packageRecommendation?.recommended,
          confidence: aiResult.proposalData?.packageRecommendation?.confidence,
          dataStructureValid: !!enhancedProposal.overview && !!enhancedProposal.packages
        }
      }
    });
    
  } catch (error: any) {
    console.error('❌ AI integration test failed:', error);
    return NextResponse.json({
      success: false,
      error: 'AI integration test failed',
      message: error.message,
      details: error.stack
    }, { status: 500 });
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'AI Integration Test Endpoint',
    usage: 'POST with { customerInfo, businessName, businessType, saveToDatabase? }',
    example: {
      customerInfo: 'We need a professional website...',
      businessName: 'Test Business',
      businessType: 'contractor',
      saveToDatabase: false
    }
  });
}
