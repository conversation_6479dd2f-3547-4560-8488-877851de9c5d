"""
FastAPI service for Pydantic AI proposal generation
Hosts the AI agent and provides REST API endpoints
"""

from fastapi import Fast<PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import asyncio
import os
import sys
from typing import Optional

# Add the lib directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src', 'lib'))

try:
    from pydantic_ai_proposal_agent import (
        generate_proposal_with_ai,
        analyze_customer_input,
        ProposalContent,
        CustomerAnalysis
    )
except ImportError as e:
    print(f"Import error: {e}")
    print("Make sure pydantic-ai and dependencies are installed")
    sys.exit(1)

# Initialize FastAPI app
app = FastAPI(
    title="GetFound AI Proposal Service",
    description="Pydantic AI service for dynamic proposal generation",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://localhost:3001"],  # Next.js dev servers
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Request/Response models
class ProposalRequest(BaseModel):
    customer_input: str
    business_name: Optional[str] = None
    business_type: Optional[str] = None

class AnalysisRequest(BaseModel):
    customer_input: str

class ProposalResponse(BaseModel):
    success: bool
    data: Optional[ProposalContent] = None
    error: Optional[str] = None

class AnalysisResponse(BaseModel):
    success: bool
    data: Optional[CustomerAnalysis] = None
    error: Optional[str] = None

@app.get("/")
async def root():
    """Health check endpoint"""
    return {
        "service": "GetFound AI Proposal Service",
        "status": "running",
        "version": "1.0.0"
    }

@app.post("/generate-proposal", response_model=ProposalResponse)
async def generate_proposal_endpoint(request: ProposalRequest):
    """
    Generate a complete proposal using Pydantic AI
    """
    try:
        if not request.customer_input.strip():
            raise HTTPException(status_code=400, detail="Customer input is required")
        
        # Generate proposal using Pydantic AI
        proposal_content = await generate_proposal_with_ai(request.customer_input)
        
        return ProposalResponse(
            success=True,
            data=proposal_content
        )
        
    except Exception as e:
        print(f"Proposal generation error: {e}")
        return ProposalResponse(
            success=False,
            error=str(e)
        )

@app.post("/analyze", response_model=AnalysisResponse)
async def analyze_customer_endpoint(request: AnalysisRequest):
    """
    Quick customer analysis for preview purposes
    """
    try:
        if not request.customer_input.strip():
            raise HTTPException(status_code=400, detail="Customer input is required")
        
        # Analyze customer input
        analysis = analyze_customer_input(request.customer_input)
        
        return AnalysisResponse(
            success=True,
            data=analysis
        )
        
    except Exception as e:
        print(f"Customer analysis error: {e}")
        return AnalysisResponse(
            success=False,
            error=str(e)
        )

@app.post("/test")
async def test_endpoint():
    """
    Test endpoint with sample data
    """
    sample_input = """
1.) Website
  - current website (click funnel) https://www.loweryfinishing.com/optin-604655891694725683997
  - I would like to have a legit website that would make it even easier for customers to request quotes.

2.) Google listing 
  - I have a few good reviews and try to get them each time we do a job for a customer. Usually we are doing work for builders and don't ask for a review. But should we? Is there a better way to show credibility with big builders?

3.) Goals
  A. Homeowners - exterior door swaps. Not repairs, or anything else. Just straight swaps.
  B.) Builders (apartments, custom homes, track homes) - We Install doors, trim, built-ins, etc.
"""
    
    try:
        # Test both analysis and proposal generation
        analysis = analyze_customer_input(sample_input)
        proposal = await generate_proposal_with_ai(sample_input)
        
        return {
            "success": True,
            "test_data": {
                "input_length": len(sample_input),
                "analysis": analysis.dict(),
                "proposal": proposal.dict(),
                "questions_count": len(proposal.questions_and_answers)
            }
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }

if __name__ == "__main__":
    import uvicorn
    
    # Check for required environment variables
    if not os.getenv("OPENAI_API_KEY"):
        print("Warning: OPENAI_API_KEY not set. AI features may not work.")
    
    print("Starting GetFound AI Proposal Service...")
    print("Service will be available at: http://localhost:8000")
    print("API docs available at: http://localhost:8000/docs")
    
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
