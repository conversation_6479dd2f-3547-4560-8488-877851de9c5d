import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { nanoid } from 'nanoid';
import { createDefaultEnhancedProposal } from '@/types/enhanced-proposal-schema';
import { generateProposalReferenceNumber } from '@/lib/reference-number-generator';

export async function POST(request: Request) {
  try {
    console.log("Create proposal API called");
    
    // Create a Supabase client that uses cookies (better for server components)
    const cookieStore = cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });
    
    // Get current user with the cookie-based client
    const { data: userData, error: userError } = await supabase.auth.getUser();
    
    if (userError) {
      console.error('Auth error:', userError);
      // Add more detail to the error response
      return NextResponse.json({ 
        error: 'Authentication failed',
        message: userError.message,
        code: userError.code || 'AUTH_ERROR',
        hint: 'Please make sure you are logged in. Try going to /debug/login-helper to fix your session.'
      }, { status: 401 });
    }
    
    if (!userData.user) {
      console.error('No user found in session');
      return NextResponse.json({ 
        error: 'User not authenticated',
        hint: 'No user found in session. Please log in through /login or try /debug/login-helper.'
      }, { status: 401 });
    }
    
    console.log('User authenticated:', userData.user.email);
    
    // Parse the request body
    const body = await request.json();
    console.log('Received proposal data:', JSON.stringify(body, null, 2));
    
    // Validate required fields
    if (!body.client_name || !body.business_name || !body.business_type) {
      return NextResponse.json({ 
        error: 'Missing required fields',
        requiredFields: ['client_name', 'business_name', 'business_type']
      }, { status: 400 });
    }
    
    // Get the template data if a template_id was provided
    let templateData = null;
    if (body.template_id) {
      const { data: template, error: templateError } = await supabase
        .from('proposal_templates')
        .select('template_data')
        .eq('id', body.template_id)
        .single();
      
      if (templateError && templateError.code !== 'PGRST116') {
        console.error('Template fetch error:', templateError);
      } else if (template) {
        templateData = template.template_data;
      }
    }
    
    // If no template_id was provided or the template wasn't found, get the default template
    if (!templateData) {
      const { data: defaultTemplate, error: defaultTemplateError } = await supabase
        .from('proposal_templates')
        .select('template_data')
        .eq('is_default', true)
        .single();
      
      if (defaultTemplateError && defaultTemplateError.code !== 'PGRST116') {
        console.error('Default template fetch error:', defaultTemplateError);
      } else if (defaultTemplate) {
        templateData = defaultTemplate.template_data;
      } else {
        // If no default template exists, use the enhanced proposal schema
        templateData = createDefaultEnhancedProposal(
          body.client_name, 
          body.business_name, 
          body.business_type
        );
      }
    }
    
    // Generate a unique URL key for sharing
    const urlKey = nanoid(10);
    
    // Create the proposal data combining the template and user input
    const proposalData = {
      client_name: body.client_name,
      business_name: body.business_name,
      business_type: body.business_type,
      reference_number: body.reference_number || await generateProposalReferenceNumber(),
      challenges: body.challenges || null,
      notes: body.notes || null,
      services: body.services || [],
      status: body.status || 'draft',
      created_by: userData.user.id,
      url_key: urlKey,
      // Create the enhanced proposal data format
      proposal_data: {
        // Start with the template (could be old or new format)
        ...templateData,
        // Ensure core client data is in the enhanced format
        clientName: body.client_name,
        businessName: body.business_name,
        businessType: body.business_type,
        // Additional user-provided data
        challenges: body.challenges || null,
        targetAudience: body.target_audience || null,
        competitorNames: body.competitor_names || null,
        websiteUrl: body.website_url || null,
        services: body.services || [],
        // Add timestamp
        createdAt: new Date().toISOString(),
        // If the template is in the old format with sections, extract packages
        packages: templateData.packages || 
                 (templateData.sections && 
                  templateData.sections.find(s => s.id === 'packages')?.packages) || 
                 createDefaultEnhancedProposal("", "", "").packages
      }
    };
    
    console.log('Creating proposal in database...');
    
    // Insert the proposal into the database
    const { data, error } = await supabase
      .from('proposals')
      .insert(proposalData)
      .select()
      .single();
    
    if (error) {
      console.error('Proposal creation error:', error);
      return NextResponse.json({ 
        error: 'Failed to create proposal',
        message: error.message,
        details: error
      }, { status: 500 });
    }
    
    console.log('Proposal created successfully, ID:', data.id);
    
    // Create a proposal_event record for the creation
    await supabase
      .from('proposal_events')
      .insert({
        proposal_id: data.id,
        event_type: 'created',
        event_data: {
          user_id: userData.user.id,
          user_email: userData.user.email
        }
      });
    
    return NextResponse.json({ 
      success: true,
      id: data.id,
      url_key: data.url_key
    });
  } catch (error: any) {
    console.error('Error creating proposal:', error);
    return NextResponse.json({ 
      error: 'Failed to create proposal',
      message: error.message,
      stack: error.stack
    }, { status: 500 });
  }
} 