#!/usr/bin/env python3
"""
Setup script for GetFound AI Proposal Service
Installs dependencies and sets up the Python AI service
"""

import subprocess
import sys
import os
from pathlib import Path

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"\n🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return result.stdout
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed:")
        print(f"Error: {e.stderr}")
        return None

def check_python_version():
    """Check if Python version is compatible"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Python 3.8 or higher is required")
        print(f"Current version: {version.major}.{version.minor}.{version.micro}")
        return False
    print(f"✅ Python version {version.major}.{version.minor}.{version.micro} is compatible")
    return True

def setup_virtual_environment():
    """Create and activate virtual environment"""
    venv_path = Path("venv")
    
    if not venv_path.exists():
        print("\n🔄 Creating virtual environment...")
        run_command("python -m venv venv", "Virtual environment creation")
    else:
        print("✅ Virtual environment already exists")
    
    # Provide activation instructions
    if os.name == 'nt':  # Windows
        activate_cmd = "venv\\Scripts\\activate"
    else:  # Unix/Linux/macOS
        activate_cmd = "source venv/bin/activate"
    
    print(f"\n📝 To activate the virtual environment, run:")
    print(f"   {activate_cmd}")
    
    return True

def install_dependencies():
    """Install Python dependencies"""
    requirements_file = Path("requirements.txt")
    
    if not requirements_file.exists():
        print("❌ requirements.txt not found")
        return False
    
    # Check if we're in a virtual environment
    if sys.prefix == sys.base_prefix:
        print("⚠️  Warning: Not in a virtual environment")
        print("   It's recommended to activate the virtual environment first")
    
    return run_command("pip install -r requirements.txt", "Installing dependencies")

def setup_environment_file():
    """Create .env file template"""
    env_file = Path(".env")
    
    if env_file.exists():
        print("✅ .env file already exists")
        return True
    
    env_template = """# GetFound AI Proposal Service Environment Variables

# OpenAI API Configuration
OPENAI_API_KEY=your_openai_api_key_here

# Logfire Configuration (optional)
LOGFIRE_TOKEN=your_logfire_token_here

# Service Configuration
PYTHON_AI_SERVICE_URL=http://localhost:8000
NODE_ENV=development

# CORS Origins (comma-separated)
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001
"""
    
    try:
        with open(env_file, 'w') as f:
            f.write(env_template)
        print("✅ Created .env template file")
        print("📝 Please edit .env and add your API keys")
        return True
    except Exception as e:
        print(f"❌ Failed to create .env file: {e}")
        return False

def main():
    """Main setup function"""
    print("🚀 GetFound AI Proposal Service Setup")
    print("=" * 50)
    
    # Change to the script directory
    script_dir = Path(__file__).parent
    os.chdir(script_dir)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Setup virtual environment
    if not setup_virtual_environment():
        sys.exit(1)
    
    # Setup environment file
    if not setup_environment_file():
        sys.exit(1)
    
    print("\n" + "=" * 50)
    print("🎉 Setup completed successfully!")
    print("\n📋 Next steps:")
    print("1. Activate the virtual environment:")
    if os.name == 'nt':
        print("   venv\\Scripts\\activate")
    else:
        print("   source venv/bin/activate")
    print("2. Install dependencies:")
    print("   pip install -r requirements.txt")
    print("3. Edit .env file and add your OpenAI API key")
    print("4. Start the service:")
    print("   python main.py")
    print("\n🌐 Service will be available at: http://localhost:8000")
    print("📚 API docs will be at: http://localhost:8000/docs")

if __name__ == "__main__":
    main()
