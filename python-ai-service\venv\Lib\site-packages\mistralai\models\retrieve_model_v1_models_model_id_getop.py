"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from .basemodelcard import BaseModelCard, BaseModelCardTypedDict
from .ftmodelcard import FTModelCard, FTModelCardTypedDict
from mistralai.types import BaseModel
from mistralai.utils import FieldMetadata, PathParamMetadata, get_discriminator
from pydantic import Discriminator, Tag
from typing import Union
from typing_extensions import Annotated, TypeAliasType, TypedDict


class RetrieveModelV1ModelsModelIDGetRequestTypedDict(TypedDict):
    model_id: str
    r"""The ID of the model to retrieve."""


class RetrieveModelV1ModelsModelIDGetRequest(BaseModel):
    model_id: Annotated[
        str, FieldMetadata(path=PathParamMetadata(style="simple", explode=False))
    ]
    r"""The ID of the model to retrieve."""


RetrieveModelV1ModelsModelIDGetResponseRetrieveModelV1ModelsModelIDGetTypedDict = TypeAliasType(
    "RetrieveModelV1ModelsModelIDGetResponseRetrieveModelV1ModelsModelIDGetTypedDict",
    Union[BaseModelCardTypedDict, FTModelCardTypedDict],
)
r"""Successful Response"""


RetrieveModelV1ModelsModelIDGetResponseRetrieveModelV1ModelsModelIDGet = Annotated[
    Union[
        Annotated[BaseModelCard, Tag("base")], Annotated[FTModelCard, Tag("fine-tuned")]
    ],
    Discriminator(lambda m: get_discriminator(m, "type", "type")),
]
r"""Successful Response"""
