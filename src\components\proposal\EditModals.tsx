import React, { useState, useEffect } from 'react';
import { EnhancedProposal } from '@/types/enhanced-proposal-schema';

interface BaseModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (data: any) => void;
  title: string;
}

interface OverviewEditModalProps extends BaseModalProps {
  formData: any;
}

interface ProjectOverviewEditModalProps extends BaseModalProps {
  proposalData: EnhancedProposal;
}

interface ClientGoalsEditModalProps extends BaseModalProps {
  proposalData: EnhancedProposal;
}

interface PackagesEditModalProps extends BaseModalProps {
  packages: any[];
}

interface NextStepsEditModalProps extends BaseModalProps {
  nextSteps: string[];
}

// Base Modal Component
const BaseModal: React.FC<{ isOpen: boolean; onClose: () => void; title: string; children: React.ReactNode }> = ({
  isOpen,
  onClose,
  title,
  children
}) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={onClose}></div>
        
        <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        
        <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
          <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg leading-6 font-medium text-gray-900">{title}</h3>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
              </button>
            </div>
            {children}
          </div>
        </div>
      </div>
    </div>
  );
};

// Overview Edit Modal
export const OverviewEditModal: React.FC<OverviewEditModalProps> = ({
  isOpen,
  onClose,
  onSave,
  title,
  formData
}) => {
  const [localData, setLocalData] = useState(formData);

  useEffect(() => {
    setLocalData(formData);
  }, [formData]);

  const handleSave = () => {
    onSave(localData);
    onClose();
  };

  const handleChange = (field: string, value: string) => {
    setLocalData(prev => ({ ...prev, [field]: value }));
  };

  return (
    <BaseModal isOpen={isOpen} onClose={onClose} title={title}>
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Client Name</label>
          <input
            type="text"
            value={localData.clientName || ''}
            onChange={(e) => handleChange('clientName', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Business Name</label>
          <input
            type="text"
            value={localData.businessName || ''}
            onChange={(e) => handleChange('businessName', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Business Type</label>
          <select
            value={localData.businessType || ''}
            onChange={(e) => handleChange('businessType', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
          >
            <option value="">Select business type</option>
            <option value="plumber">Plumber</option>
            <option value="electrician">Electrician</option>
            <option value="landscaper">Landscaper</option>
            <option value="painter">Painter</option>
            <option value="hvac_technician">HVAC Technician</option>
            <option value="general_contractor">General Contractor</option>
            <option value="roofer">Roofer</option>
            <option value="cleaning_service">Cleaning Service</option>
            <option value="carpenter">Carpenter</option>
            <option value="handyman">Handyman</option>
            <option value="cnc_machining">CNC Machining</option>
            <option value="other">Other</option>
          </select>
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Reference Number</label>
          <input
            type="text"
            value={localData.referenceNumber || ''}
            onChange={(e) => handleChange('referenceNumber', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
          <select
            value={localData.status || 'draft'}
            onChange={(e) => handleChange('status', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
          >
            <option value="draft">Draft</option>
            <option value="ready">Ready to Send</option>
            <option value="sent">Sent</option>
            <option value="accepted">Accepted</option>
            <option value="declined">Declined</option>
          </select>
        </div>
      </div>
      
      <div className="mt-6 flex justify-end space-x-3">
        <button
          onClick={onClose}
          className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
        >
          Cancel
        </button>
        <button
          onClick={handleSave}
          className="px-4 py-2 text-sm font-medium text-white bg-primary-600 border border-transparent rounded-md hover:bg-primary-700"
        >
          Save Changes
        </button>
      </div>
    </BaseModal>
  );
};

// Project Overview Edit Modal
export const ProjectOverviewEditModal: React.FC<ProjectOverviewEditModalProps> = ({
  isOpen,
  onClose,
  onSave,
  title,
  proposalData
}) => {
  const [description, setDescription] = useState(proposalData.overview?.description || '');

  useEffect(() => {
    setDescription(proposalData.overview?.description || '');
  }, [proposalData]);

  const handleSave = () => {
    onSave({
      overview: {
        ...proposalData.overview,
        description
      }
    });
    onClose();
  };

  return (
    <BaseModal isOpen={isOpen} onClose={onClose} title={title}>
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Project Description</label>
          <textarea
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            rows={6}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
            placeholder="Describe the project overview and approach..."
          />
        </div>
      </div>
      
      <div className="mt-6 flex justify-end space-x-3">
        <button
          onClick={onClose}
          className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
        >
          Cancel
        </button>
        <button
          onClick={handleSave}
          className="px-4 py-2 text-sm font-medium text-white bg-primary-600 border border-transparent rounded-md hover:bg-primary-700"
        >
          Save Changes
        </button>
      </div>
    </BaseModal>
  );
};

// Client Goals Edit Modal
export const ClientGoalsEditModal: React.FC<ClientGoalsEditModalProps> = ({
  isOpen,
  onClose,
  onSave,
  title,
  proposalData
}) => {
  // Handle both old format (array) and new format (object with goals property)
  const getGoalsArray = (clientGoals: any) => {
    if (Array.isArray(clientGoals)) {
      // Old format: clientGoals is directly an array
      return clientGoals.map(goal =>
        typeof goal === 'string' ? { title: goal, description: '' } : goal
      );
    } else if (clientGoals && Array.isArray(clientGoals.goals)) {
      // New format: clientGoals.goals is an array of strings
      return clientGoals.goals.map((goal: string) => ({ title: goal, description: '' }));
    }
    return [];
  };

  const [goals, setGoals] = useState(getGoalsArray(proposalData.clientGoals));

  useEffect(() => {
    setGoals(getGoalsArray(proposalData.clientGoals));
  }, [proposalData]);

  const handleSave = () => {
    // Save in the new format to maintain consistency
    onSave({
      clientGoals: {
        title: proposalData.clientGoals?.title || "Understanding Your Goals",
        description: proposalData.clientGoals?.description || "Based on our discussion, we've identified your key objectives",
        goals: goals.map(goal => goal.title || goal.description || goal)
      }
    });
    onClose();
  };

  const addGoal = () => {
    setGoals([...goals, { title: '', description: '' }]);
  };

  const updateGoal = (index: number, field: string, value: string) => {
    const updatedGoals = [...goals];
    updatedGoals[index] = { ...updatedGoals[index], [field]: value };
    setGoals(updatedGoals);
  };

  const removeGoal = (index: number) => {
    setGoals(goals.filter((_, i) => i !== index));
  };

  return (
    <BaseModal isOpen={isOpen} onClose={onClose} title={title}>
      <div className="space-y-4 max-h-96 overflow-y-auto">
        {goals && goals.length > 0 ? goals.map((goal, index) => (
          <div key={index} className="border border-gray-200 rounded-lg p-3">
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm font-medium text-gray-700">Goal {index + 1}</span>
              <button
                onClick={() => removeGoal(index)}
                className="text-red-500 hover:text-red-700"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                </svg>
              </button>
            </div>
            <input
              type="text"
              placeholder="Goal title"
              value={goal.title || goal || ''}
              onChange={(e) => updateGoal(index, 'title', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 mb-2"
            />
            <textarea
              placeholder="Goal description"
              value={goal.description || ''}
              onChange={(e) => updateGoal(index, 'description', e.target.value)}
              rows={2}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
            />
          </div>
        )) : (
          <div className="text-center text-gray-500 py-4">
            No goals added yet. Click "Add Goal" to get started.
          </div>
        )}
        
        <button
          onClick={addGoal}
          className="w-full py-2 border-2 border-dashed border-gray-300 rounded-lg text-gray-600 hover:border-gray-400 hover:text-gray-700"
        >
          + Add Goal
        </button>
      </div>
      
      <div className="mt-6 flex justify-end space-x-3">
        <button
          onClick={onClose}
          className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
        >
          Cancel
        </button>
        <button
          onClick={handleSave}
          className="px-4 py-2 text-sm font-medium text-white bg-primary-600 border border-transparent rounded-md hover:bg-primary-700"
        >
          Save Changes
        </button>
      </div>
    </BaseModal>
  );
};

// Packages Edit Modal
export const PackagesEditModal: React.FC<PackagesEditModalProps> = ({
  isOpen,
  onClose,
  onSave,
  title,
  packages
}) => {
  const [localPackages, setLocalPackages] = useState(packages);

  useEffect(() => {
    setLocalPackages(packages);
  }, [packages]);

  const handleSave = () => {
    onSave(localPackages);
    onClose();
  };

  const updatePackage = (index: number, field: string, value: any) => {
    const updated = [...localPackages];
    updated[index] = { ...updated[index], [field]: value };
    setLocalPackages(updated);
  };

  const updateFeature = (packageIndex: number, featureIndex: number, value: string) => {
    const updated = [...localPackages];
    updated[packageIndex].features[featureIndex] = value;
    setLocalPackages(updated);
  };

  const addFeature = (packageIndex: number) => {
    const updated = [...localPackages];
    updated[packageIndex].features.push('');
    setLocalPackages(updated);
  };

  const removeFeature = (packageIndex: number, featureIndex: number) => {
    const updated = [...localPackages];
    updated[packageIndex].features.splice(featureIndex, 1);
    setLocalPackages(updated);
  };

  return (
    <BaseModal isOpen={isOpen} onClose={onClose} title={title}>
      <div className="space-y-6 max-h-96 overflow-y-auto">
        {localPackages.map((pkg, packageIndex) => (
          <div key={packageIndex} className="border border-gray-200 rounded-lg p-4">
            <div className="flex justify-between items-center mb-3">
              <h4 className="font-medium text-gray-900">Package {packageIndex + 1}</h4>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={pkg.isRecommended || false}
                  onChange={(e) => updatePackage(packageIndex, 'isRecommended', e.target.checked)}
                  className="mr-2"
                />
                <span className="text-sm">Recommended</span>
              </label>
            </div>

            <div className="space-y-3">
              <input
                type="text"
                placeholder="Package name"
                value={pkg.name}
                onChange={(e) => updatePackage(packageIndex, 'name', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
              />

              <div className="grid grid-cols-2 gap-3">
                <input
                  type="text"
                  placeholder="Setup fee"
                  value={pkg.setupFee}
                  onChange={(e) => updatePackage(packageIndex, 'setupFee', e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                />
                <input
                  type="text"
                  placeholder="Monthly fee"
                  value={pkg.monthlyFee}
                  onChange={(e) => updatePackage(packageIndex, 'monthlyFee', e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Features</label>
                {pkg.features.map((feature, featureIndex) => (
                  <div key={featureIndex} className="flex items-center mb-2">
                    <input
                      type="text"
                      value={feature}
                      onChange={(e) => updateFeature(packageIndex, featureIndex, e.target.value)}
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                    />
                    <button
                      onClick={() => removeFeature(packageIndex, featureIndex)}
                      className="ml-2 text-red-500 hover:text-red-700"
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                      </svg>
                    </button>
                  </div>
                ))}
                <button
                  onClick={() => addFeature(packageIndex)}
                  className="text-sm text-blue-600 hover:text-blue-700"
                >
                  + Add Feature
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="mt-6 flex justify-end space-x-3">
        <button
          onClick={onClose}
          className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
        >
          Cancel
        </button>
        <button
          onClick={handleSave}
          className="px-4 py-2 text-sm font-medium text-white bg-primary-600 border border-transparent rounded-md hover:bg-primary-700"
        >
          Save Changes
        </button>
      </div>
    </BaseModal>
  );
};

// Next Steps Edit Modal
export const NextStepsEditModal: React.FC<NextStepsEditModalProps> = ({
  isOpen,
  onClose,
  onSave,
  title,
  nextSteps
}) => {
  const [localSteps, setLocalSteps] = useState(nextSteps);

  useEffect(() => {
    setLocalSteps(nextSteps);
  }, [nextSteps]);

  const handleSave = () => {
    onSave(localSteps);
    onClose();
  };

  const updateStep = (index: number, value: string) => {
    const updated = [...localSteps];
    updated[index] = value;
    setLocalSteps(updated);
  };

  const addStep = () => {
    setLocalSteps([...localSteps, '']);
  };

  const removeStep = (index: number) => {
    setLocalSteps(localSteps.filter((_, i) => i !== index));
  };

  return (
    <BaseModal isOpen={isOpen} onClose={onClose} title={title}>
      <div className="space-y-4 max-h-96 overflow-y-auto">
        {localSteps.map((step, index) => (
          <div key={index} className="flex items-start">
            <div className="flex-shrink-0 w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-medium mr-3 mt-2">
              {index + 1}
            </div>
            <div className="flex-1">
              <textarea
                value={step}
                onChange={(e) => updateStep(index, e.target.value)}
                rows={2}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                placeholder={`Step ${index + 1}`}
              />
            </div>
            <button
              onClick={() => removeStep(index)}
              className="ml-2 mt-2 text-red-500 hover:text-red-700"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
              </svg>
            </button>
          </div>
        ))}

        <button
          onClick={addStep}
          className="w-full py-2 border-2 border-dashed border-gray-300 rounded-lg text-gray-600 hover:border-gray-400 hover:text-gray-700"
        >
          + Add Step
        </button>
      </div>

      <div className="mt-6 flex justify-end space-x-3">
        <button
          onClick={onClose}
          className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
        >
          Cancel
        </button>
        <button
          onClick={handleSave}
          className="px-4 py-2 text-sm font-medium text-white bg-primary-600 border border-transparent rounded-md hover:bg-primary-700"
        >
          Save Changes
        </button>
      </div>
    </BaseModal>
  );
};
