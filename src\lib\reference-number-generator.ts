import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

// Create a service role client for admin operations
const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey);

/**
 * Generates a sequential proposal reference number in format: GF-YYYY-NNNN
 * @returns Promise<string> - The generated reference number
 */
export async function generateProposalReferenceNumber(): Promise<string> {
  const currentYear = new Date().getFullYear();
  const yearPrefix = `GF-${currentYear}`;
  
  try {
    // Get the highest reference number for the current year
    const { data: existingProposals, error } = await supabaseAdmin
      .from('proposals')
      .select('reference_number')
      .like('reference_number', `${yearPrefix}-%`)
      .order('reference_number', { ascending: false })
      .limit(1);
    
    if (error) {
      console.error('Error fetching existing proposals:', error);
      // Fallback to timestamp-based reference if database query fails
      return `GF-${currentYear}-${Date.now().toString().slice(-4)}`;
    }
    
    let nextNumber = 1;
    
    if (existingProposals && existingProposals.length > 0) {
      const lastReference = existingProposals[0].reference_number;
      
      // Extract the number part from the reference (e.g., "GF-2025-0123" -> "0123")
      const numberPart = lastReference.split('-')[2];
      
      if (numberPart) {
        const lastNumber = parseInt(numberPart, 10);
        nextNumber = lastNumber + 1;
      }
    }
    
    // Format the number with leading zeros (minimum 4 digits)
    const formattedNumber = nextNumber.toString().padStart(4, '0');
    
    return `${yearPrefix}-${formattedNumber}`;
    
  } catch (error) {
    console.error('Error generating reference number:', error);
    // Fallback to timestamp-based reference
    return `GF-${currentYear}-${Date.now().toString().slice(-4)}`;
  }
}

/**
 * Validates a proposal reference number format
 * @param referenceNumber - The reference number to validate
 * @returns boolean - True if valid format
 */
export function validateReferenceNumber(referenceNumber: string): boolean {
  const pattern = /^GF-\d{4}-\d{4,}$/;
  return pattern.test(referenceNumber);
}

/**
 * Extracts year and sequence number from a reference number
 * @param referenceNumber - The reference number to parse
 * @returns object with year and sequenceNumber, or null if invalid
 */
export function parseReferenceNumber(referenceNumber: string): { year: number; sequenceNumber: number } | null {
  if (!validateReferenceNumber(referenceNumber)) {
    return null;
  }
  
  const parts = referenceNumber.split('-');
  const year = parseInt(parts[1], 10);
  const sequenceNumber = parseInt(parts[2], 10);
  
  return { year, sequenceNumber };
}

/**
 * Gets the next available reference number for testing purposes
 * @param year - Optional year (defaults to current year)
 * @returns Promise<string> - The next reference number that would be generated
 */
export async function getNextReferenceNumber(year?: number): Promise<string> {
  const targetYear = year || new Date().getFullYear();
  const yearPrefix = `GF-${targetYear}`;
  
  try {
    const { data: existingProposals, error } = await supabaseAdmin
      .from('proposals')
      .select('reference_number')
      .like('reference_number', `${yearPrefix}-%`)
      .order('reference_number', { ascending: false })
      .limit(1);
    
    if (error) {
      throw error;
    }
    
    let nextNumber = 1;
    
    if (existingProposals && existingProposals.length > 0) {
      const lastReference = existingProposals[0].reference_number;
      const numberPart = lastReference.split('-')[2];
      
      if (numberPart) {
        const lastNumber = parseInt(numberPart, 10);
        nextNumber = lastNumber + 1;
      }
    }
    
    const formattedNumber = nextNumber.toString().padStart(4, '0');
    return `${yearPrefix}-${formattedNumber}`;
    
  } catch (error) {
    console.error('Error getting next reference number:', error);
    throw error;
  }
}
