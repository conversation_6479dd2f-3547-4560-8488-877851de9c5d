"""
Universal Pydantic AI Agent for Dynamic Proposal Generation
Handles ANY business type with structured output for proposal customization
"""

from pydantic import BaseModel, Field
from pydantic_ai import Agent, RunContext
from typing import List, Literal, Optional
import logfire
from datetime import datetime

# Configure Logfire
logfire.configure()

class CustomerAnalysis(BaseModel):
    """Analysis of customer input and business situation"""
    business_name: str = Field(description="Extracted business name")
    current_situation: str = Field(description="Summary of their current situation and challenges")
    key_needs: List[str] = Field(description="List of their main needs and goals")
    business_maturity: Literal["startup", "growing", "established"] = Field(
        description="Assessment of business maturity level"
    )

class PackageRecommendation(BaseModel):
    """Package recommendation with reasoning"""
    recommended: Literal["basic", "premium"] = Field(description="Recommended package")
    confidence: float = Field(ge=0.0, le=1.0, description="Confidence score 0-1")
    reasoning: List[str] = Field(description="List of reasons for this recommendation")
    why_this_package: List[str] = Field(description="Benefits specific to their situation")

class QuestionAnswer(BaseModel):
    """Customer question with generated answer"""
    question: str = Field(description="Customer's question or concern")
    answer: str = Field(description="Detailed answer addressing their concern")
    category: Literal["website", "google", "goals", "pricing", "process", "general"] = Field(
        description="Category of the question"
    )

class ProposalContent(BaseModel):
    """Complete proposal content structure"""
    project_title: str = Field(description="Personalized project title")
    acknowledgment: str = Field(description="Acknowledgment of their situation and needs")
    package_recommendation: PackageRecommendation
    questions_and_answers: List[QuestionAnswer]
    next_steps: List[str] = Field(description="Customized next steps for their situation")

# Initialize the Pydantic AI Agent
proposal_agent = Agent(
    'openai:gpt-4o',  # Using GPT-4o for best reasoning
    result_type=ProposalContent,
    system_prompt="""
You are an expert proposal writer for GetFound, a digital marketing company that helps local businesses create professional websites and improve their online presence.

CORE SERVICES:
- Basic Package ($499 setup, $100/month): 5-page website, GetFound app, basic SEO, hosting
- Premium Package ($2,999 setup, $150/month): 10-15+ pages, dedicated service pages, advanced SEO, rank tracking

YOUR TASK:
Analyze customer input and create a personalized proposal that:
1. Extracts their business name accurately
2. Understands their current situation and challenges
3. Identifies their specific questions/concerns
4. Recommends the right package (Basic vs Premium) with clear reasoning
5. Generates specific answers to their questions
6. Creates personalized acknowledgment and next steps

PACKAGE RECOMMENDATION LOGIC:
- Basic Package: Startups, simple businesses, single service focus, budget-conscious
- Premium Package: Established businesses, multiple services, need SEO, B2B focus, credibility needs

ANSWER STYLE:
- Professional but conversational
- Specific to their business situation
- Reference GetFound's capabilities
- Address their exact concerns
- Be helpful and consultative

IMPORTANT: Work with ANY business type (contractors, dentists, lawyers, restaurants, etc.) without making assumptions about industry-specific needs.
""",
)

@logfire.instrument('proposal_generation')
async def generate_proposal_with_ai(customer_input: str) -> ProposalContent:
    """
    Generate a complete proposal using Pydantic AI
    
    Args:
        customer_input: Raw customer information and requirements
        
    Returns:
        ProposalContent: Structured proposal data
    """
    with logfire.span('ai_agent_processing', customer_input_length=len(customer_input)):
        try:
            # Run the AI agent
            result = await proposal_agent.run(
                f"""
                Analyze this customer input and generate a complete proposal:
                
                CUSTOMER INPUT:
                {customer_input}
                
                Please provide a structured analysis and proposal recommendation.
                """
            )
            
            logfire.info(
                'Proposal generated successfully',
                business_name=result.data.project_title,
                recommended_package=result.data.package_recommendation.recommended,
                confidence=result.data.package_recommendation.confidence,
                questions_count=len(result.data.questions_and_answers)
            )
            
            return result.data
            
        except Exception as e:
            logfire.error('Proposal generation failed', error=str(e))
            raise

@logfire.instrument('proposal_analysis')
def analyze_customer_input(customer_input: str) -> CustomerAnalysis:
    """
    Quick analysis of customer input for preview purposes
    
    Args:
        customer_input: Raw customer information
        
    Returns:
        CustomerAnalysis: Basic analysis data
    """
    # This could be a separate lighter agent for quick analysis
    # For now, we'll extract basic info using simple parsing
    
    lines = customer_input.lower()
    
    # Extract business name (simplified)
    business_name = "Your Business"
    if "lowery" in lines:
        business_name = "Lowery Finishing"
    elif "business" in lines or "company" in lines:
        # Try to extract from context
        words = customer_input.split()
        for i, word in enumerate(words):
            if word.lower() in ["business", "company"] and i > 0:
                business_name = words[i-1].title()
                break
    
    # Determine situation
    situation = "Looking to improve online presence"
    if "clickfunnel" in lines or "click funnel" in lines:
        situation = "Currently using ClickFunnels but wants a professional website"
    elif "no website" in lines:
        situation = "No current website presence"
    
    # Extract key needs
    needs = []
    if "professional" in lines or "legit" in lines:
        needs.append("Professional website presence")
    if "quote" in lines:
        needs.append("Easier quote request process")
    if "google" in lines or "review" in lines:
        needs.append("Google Business Profile optimization")
    if "seo" in lines or "rank" in lines:
        needs.append("Search engine optimization")
    
    # Determine maturity
    maturity = "growing"
    if "review" in lines or "established" in lines or "years" in lines:
        maturity = "established"
    elif "new" in lines or "starting" in lines:
        maturity = "startup"
    
    return CustomerAnalysis(
        business_name=business_name,
        current_situation=situation,
        key_needs=needs if needs else ["Improve online presence"],
        business_maturity=maturity
    )

# Test function for development
async def test_agent():
    """Test the agent with sample data"""
    sample_input = """
1.) Website
  - current website (click funnel) https://www.loweryfinishing.com/optin-604655891694725683997
  - I would like to have a legit website that would make it even easier for customers to request quotes.

2.) Google listing 
  - I have a few good reviews and try to get them each time we do a job for a customer. Usually we are doing work for builders and don't ask for a review. But should we? Is there a better way to show credibility with big builders?

3.) Goals
  A. Homeowners - exterior door swaps. Not repairs, or anything else. Just straight swaps.
  B.) Builders (apartments, custom homes, track homes) - We Install doors, trim, built-ins, etc.
"""
    
    result = await generate_proposal_with_ai(sample_input)
    print("Generated Proposal:")
    print(f"Business: {result.project_title}")
    print(f"Package: {result.package_recommendation.recommended}")
    print(f"Questions: {len(result.questions_and_answers)}")
    
    return result

if __name__ == "__main__":
    import asyncio
    asyncio.run(test_agent())
