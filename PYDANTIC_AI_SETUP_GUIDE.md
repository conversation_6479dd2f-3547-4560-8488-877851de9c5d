# 🚀 Pydantic AI Proposal System Setup Guide

Complete setup guide for the universal Pydantic AI proposal generation system.

## 📋 Overview

This system provides:
- **Universal AI Agent**: Works for ANY business type (contractors, dentists, lawyers, etc.)
- **Structured Output**: Type-safe proposal generation using Pydantic models
- **Question Extraction**: Automatically identifies and answers customer questions
- **Package Recommendations**: Intelligent Basic vs Premium recommendations
- **Logfire Monitoring**: Comprehensive tracing and performance monitoring

## 🛠️ Prerequisites

- **Python 3.8+** (for AI service)
- **Node.js 18+** (for Next.js integration)
- **OpenAI API Key** (for AI functionality)
- **Logfire Account** (optional, for monitoring)

## 📦 Installation Steps

### Step 1: Setup Python AI Service

```bash
# Navigate to Python service directory
cd python-ai-service

# Run setup script
python setup.py

# Activate virtual environment
source venv/bin/activate  # Linux/macOS
# or
venv\Scripts\activate     # Windows

# Install dependencies
pip install -r requirements.txt
```

### Step 2: Configure Environment Variables

Edit `python-ai-service/.env`:

```env
# Required
OPENAI_API_KEY=your_openai_api_key_here

# Optional (for monitoring)
LOGFIRE_TOKEN=your_logfire_token_here

# Service configuration
PYTHON_AI_SERVICE_URL=http://localhost:8000
NODE_ENV=development
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001
```

### Step 3: Start Python AI Service

```bash
# From python-ai-service directory
python start.py

# Or manually
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

Service will be available at:
- **API**: http://localhost:8000
- **Docs**: http://localhost:8000/docs

### Step 4: Update Next.js Environment

Add to your `.env.local`:

```env
PYTHON_AI_SERVICE_URL=http://localhost:8000
```

### Step 5: Test Integration

```bash
# Install test dependencies
npm install node-fetch

# Run integration tests
node test-pydantic-ai-integration.js
```

## 🧪 Testing the System

### Test with Lowery Finishing Example

1. **Start both services**:
   - Python AI service on port 8000
   - Next.js dev server on port 3000

2. **Test API endpoint**:
   ```bash
   curl -X GET http://localhost:3000/api/generate-dynamic-proposal
   ```

3. **Expected results**:
   - Business name: "Lowery Finishing"
   - Package recommendation: "Premium" (high confidence)
   - Questions extracted:
     - "Should I ask builders for Google reviews?"
     - "How can I fix the automatic photo issue?"
     - "How will you help me focus on exterior door swaps?"

### Test with Different Business Types

Try these examples to verify universal functionality:

**Dental Practice:**
```
I'm a dentist looking to attract more patients. Currently have an outdated website and want to improve online booking and showcase our services better.
```

**Law Firm:**
```
We're a small law firm specializing in personal injury. Need a professional website that builds trust and makes it easy for potential clients to contact us.
```

**Restaurant:**
```
Family restaurant wanting to increase online orders and showcase our menu. Currently using social media but need a proper website.
```

## 🔧 Integration Points

### 1. API Endpoint Updates

The `/api/generate-dynamic-proposal` endpoint now:
- Calls Pydantic AI service for proposal generation
- Returns structured proposal data
- Handles fallback if AI service is unavailable
- Saves results to Supabase

### 2. Proposal Preview Updates

The `InteractiveProposalPreview` component now:
- Displays dynamic content from AI
- Shows extracted Q&A sections
- Highlights package recommendations with reasoning
- Renders personalized acknowledgments

### 3. Database Schema

Proposals now store:
- Raw customer input in `notes` field
- AI-generated analysis in `challenges` field
- Package recommendation in `package_type` field
- Complete proposal structure in `proposal_data` JSONB

## 📊 Monitoring with Logfire

### Setup Logfire Integration

1. **Get Logfire token** from https://logfire.pydantic.dev
2. **Add to .env**: `LOGFIRE_TOKEN=your_token_here`
3. **View traces** in Logfire dashboard

### What Gets Tracked

- **Request Processing**: Input analysis and processing time
- **AI Decision Making**: Package recommendation logic and confidence
- **Question Extraction**: Number and types of questions found
- **Error Handling**: Failed requests and fallback usage
- **Performance Metrics**: Response times and success rates

## 🚨 Troubleshooting

### Common Issues

1. **Python Service Won't Start**
   ```bash
   # Check Python version
   python --version  # Should be 3.8+
   
   # Check dependencies
   pip list | grep pydantic-ai
   
   # Check environment
   cat .env | grep OPENAI_API_KEY
   ```

2. **Next.js Integration Fails**
   ```bash
   # Check service is running
   curl http://localhost:8000/
   
   # Check CORS configuration
   # Ensure Next.js is on allowed origins
   ```

3. **AI Responses Are Poor**
   - Verify OpenAI API key is valid
   - Check API usage limits
   - Review system prompt in `pydantic_ai_proposal_agent.py`

4. **Database Sync Issues**
   - Check Supabase connection
   - Verify proposal schema matches new structure
   - Review API endpoint error logs

### Debug Mode

Enable detailed logging:

```bash
# Python service
export LOG_LEVEL=debug
python start.py

# Next.js
export DEBUG=1
npm run dev
```

## 🔄 Development Workflow

### Adding New Question Categories

1. **Update Python model**:
   ```python
   # In pydantic_ai_proposal_agent.py
   category: Literal["website", "google", "goals", "pricing", "process", "general", "new_category"]
   ```

2. **Update system prompt** with examples for new category

3. **Test with relevant input** to verify extraction

### Improving Package Recommendations

1. **Modify system prompt** in `pydantic_ai_proposal_agent.py`
2. **Add new reasoning factors** to recommendation logic
3. **Test with various business scenarios**
4. **Monitor confidence scores** in Logfire

### Customizing Response Templates

1. **Update system prompt** for different response styles
2. **Modify Pydantic models** for additional fields
3. **Update TypeScript interfaces** to match
4. **Test integration** end-to-end

## 📈 Performance Optimization

### Response Time Improvements

- **Caching**: Implement Redis for repeated requests
- **Parallel Processing**: Run analysis and generation concurrently
- **Model Selection**: Use GPT-3.5-turbo for faster responses if quality is acceptable

### Scaling Considerations

- **Load Balancing**: Multiple Python service instances
- **Rate Limiting**: Implement request throttling
- **Database Optimization**: Index frequently queried fields
- **CDN**: Cache static proposal templates

## 🔒 Security Best Practices

- **API Key Protection**: Never expose in client-side code
- **Input Validation**: Sanitize all customer input
- **CORS Configuration**: Restrict to known origins
- **Rate Limiting**: Prevent abuse of AI endpoints
- **Error Handling**: Don't expose internal details

## 📝 Next Steps

1. **Test thoroughly** with various business types
2. **Monitor performance** using Logfire
3. **Gather feedback** on proposal quality
4. **Iterate on prompts** based on results
5. **Scale infrastructure** as needed

## 🎯 Success Metrics

- **Response Time**: < 5 seconds for proposal generation
- **Accuracy**: > 90% correct business name extraction
- **Question Coverage**: > 80% of customer questions addressed
- **Package Accuracy**: > 85% appropriate package recommendations
- **User Satisfaction**: Positive feedback on proposal quality

---

🎉 **Congratulations!** You now have a universal AI-powered proposal system that can handle any business type with intelligent question extraction and package recommendations.
