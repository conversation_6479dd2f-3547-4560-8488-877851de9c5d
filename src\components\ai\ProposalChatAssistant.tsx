import React, { useState, useRef, useEffect } from 'react';
import { EnhancedProposal } from '@/types/enhanced-proposal-schema';
import { CustomerProfile, analyzeCustomerProfile, analyzeLoweryFinishing } from '@/lib/ai-proposal-analyzer';
import { processProposalChatMessage, ChatContext } from '@/lib/ai-proposal-chat';
import VoiceInput from './VoiceInput';

interface ProposalChatAssistantProps {
  proposalId: string;
  chatHistory: any[];
  onChatHistoryChange: (history: any[]) => void;
  proposalData: EnhancedProposal;
  onProposalUpdate?: (updatedData: any) => void;
  customerProfile?: CustomerProfile;
}

const ProposalChatAssistant: React.FC<ProposalChatAssistantProps> = ({
  proposalId,
  chatHistory,
  onChatHistoryChange,
  proposalData,
  onProposalUpdate,
  customerProfile
}) => {
  const [chatMessage, setChatMessage] = useState('');
  const [isSendingMessage, setIsSendingMessage] = useState(false);
  const [selectedSection, setSelectedSection] = useState<string>('');
  const [error, setError] = useState<string | null>(null);
  const [packageRecommendation, setPackageRecommendation] = useState<any>(null);
  const [isVoiceInputActive, setIsVoiceInputActive] = useState(false);
  const [voicePreviewText, setVoicePreviewText] = useState('');
  const chatEndRef = useRef<HTMLDivElement>(null);

  // Initialize with customer analysis if profile is available
  useEffect(() => {
    if (customerProfile && !packageRecommendation) {
      const recommendation = analyzeCustomerProfile(customerProfile);
      setPackageRecommendation(recommendation);

      // Add initial analysis message to chat
      const analysisMessage = {
        role: 'assistant' as const,
        content: `I've analyzed ${customerProfile.businessName} and recommend the **${recommendation.recommendedPackage.toUpperCase()} package** with ${Math.round(recommendation.confidence * 100)}% confidence.

**Key Reasoning:**
${recommendation.reasoning.map(r => `• ${r}`).join('\n')}

I can help customize this proposal based on their specific needs. What would you like me to update?`,
        timestamp: new Date().toISOString()
      };

      if (chatHistory.length === 0) {
        onChatHistoryChange([analysisMessage]);
      }
    }
  }, [customerProfile, packageRecommendation, chatHistory.length, onChatHistoryChange]);
  
  // Section options for quick selection
  const sectionOptions = [
    { id: 'overview', label: 'Project Overview' },
    { id: 'clientGoals', label: 'Client Goals' },
    { id: 'packages', label: 'Packages & Features' },
    { id: 'specificQuestions', label: 'Specific Questions' },
    { id: 'nextSteps', label: 'Next Steps' },
    { id: 'addons', label: 'Add-on Services' }
  ];
  
  // Scroll to bottom effect for chat - only when user sends a message
  useEffect(() => {
    if (chatEndRef.current && chatHistory.length > 0) {
      // Only auto-scroll if the last message is from the user or assistant (not on initial load)
      const lastMessage = chatHistory[chatHistory.length - 1];
      if (lastMessage && (lastMessage.role === 'user' || lastMessage.role === 'assistant')) {
        // Small delay to ensure content is rendered
        setTimeout(() => {
          if (chatEndRef.current) {
            chatEndRef.current.scrollIntoView({ behavior: 'smooth' });
          }
        }, 100);
      }
    }
  }, [chatHistory]);

  // Voice input handlers
  const handleVoiceTranscription = (text: string) => {
    setChatMessage(text);
    setVoicePreviewText('');
    setIsVoiceInputActive(false);
  };

  const handleVoiceTranscriptionUpdate = (text: string) => {
    setVoicePreviewText(text);
    setIsVoiceInputActive(true);
  };

  // Handle sending messages to AI assistant
  const handleSendMessage = async () => {
    if (!chatMessage.trim() || isSendingMessage) return;

    setIsSendingMessage(true);
    setError(null);

    const messageToSend = chatMessage; // Store message before clearing

    try {
      // Add the user message to chat history immediately for better UX
      const updatedChatHistory = [
        ...chatHistory,
        {
          role: 'user',
          content: messageToSend,
          timestamp: new Date().toISOString()
        }
      ];

      onChatHistoryChange(updatedChatHistory);
      setChatMessage('');

      // Send the message to the AI assistant
      const response = await fetch('/api/ai-proposal-assistant', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          proposalId: proposalId,
          message: messageToSend,
          section: selectedSection || undefined
        }),
        credentials: 'include'
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to get AI response');
      }
      
      const data = await response.json();

      // Update chat history with the complete history from the server
      if (data.chatHistory) {
        onChatHistoryChange(data.chatHistory);
      }

      // Handle proposal updates from AI
      if (data.updatedProposalData && onProposalUpdate) {
        console.log('AI updated proposal data:', data.updatedProposalData);
        onProposalUpdate(data.updatedProposalData);
      }

      // Reset selected section after sending
      setSelectedSection('');
    } catch (error: any) {
      console.error('Error sending message to AI:', error);
      // Add error message to chat
      onChatHistoryChange([
        ...chatHistory,
        {
          role: 'system',
          content: 'Sorry, there was an error processing your request. Please try again.',
          timestamp: new Date().toISOString()
        }
      ]);
    } finally {
      setIsSendingMessage(false);
    }
  };
  
  // Format chat timestamp
  const formatChatTime = (timestamp: string) => {
    try {
      const date = new Date(timestamp);
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } catch (e) {
      return '';
    }
  };
  
  // Generate section-specific placeholders
  const getPlaceholder = () => {
    if (!selectedSection) return "Ask the AI assistant...";
    
    switch (selectedSection) {
      case 'overview':
        return `Help me write an overview for ${proposalData.businessName}...`;
      case 'clientGoals':
        return `Help identify key goals for ${proposalData.businessName}...`;
      case 'packages':
        return `Help improve package descriptions for ${proposalData.businessType} business...`;
      case 'specificQuestions':
        return `Help address questions a ${proposalData.businessType} might have...`;
      case 'nextSteps':
        return `Suggest next steps for this proposal...`;
      default:
        return `Help me with the ${selectedSection} section...`;
    }
  };
  
  return (
    <div className="flex flex-col bg-white shadow rounded-lg h-full">
      <div className="bg-blue-50 p-4 border-b border-blue-100">
        <h3 className="text-lg font-medium text-gray-900 flex items-center">
          <svg className="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
          </svg>
          AI Proposal Assistant
        </h3>
        <p className="text-sm text-gray-600">
          Ask questions or get help with specific sections
        </p>
      </div>
      
      <div className="flex-grow overflow-y-auto p-4 bg-gray-50 space-y-4">
        {chatHistory.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <svg className="w-12 h-12 mx-auto text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z"></path>
            </svg>
            <p className="mt-2">
              No messages yet. Ask the AI assistant for help with your proposal.
            </p>
          </div>
        ) : (
          chatHistory.map((message, index) => (
            <div 
              key={index} 
              className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
            >
              <div 
                className={`max-w-[80%] rounded-lg p-3 ${
                  message.role === 'user' 
                    ? 'bg-primary-100 text-primary-800'
                    : message.role === 'system'
                      ? 'bg-red-100 text-red-800'
                      : 'bg-white border border-gray-200'
                }`}
              >
                <div className="text-sm whitespace-pre-line">{message.content}</div>
                {message.timestamp && (
                  <div className="text-xs text-gray-500 mt-1 text-right">
                    {formatChatTime(message.timestamp)}
                  </div>
                )}
              </div>
            </div>
          ))
        )}
        <div ref={chatEndRef} />
      </div>
      
      {/* Quick Actions */}
      {customerProfile && (
        <div className="p-2 border-t border-gray-200 bg-blue-50">
          <div className="text-xs text-blue-700 mb-2 font-medium">Quick Actions:</div>
          <div className="flex flex-wrap gap-2">
            <button
              onClick={() => setChatMessage("Analyze this customer and recommend the best package")}
              className="px-2 py-1 text-xs bg-blue-100 text-blue-700 rounded hover:bg-blue-200"
            >
              📊 Analyze Customer
            </button>
            <button
              onClick={() => setChatMessage("Update the proposal overview for this customer")}
              className="px-2 py-1 text-xs bg-green-100 text-green-700 rounded hover:bg-green-200"
            >
              ✏️ Update Overview
            </button>
            <button
              onClick={() => setChatMessage("Customize the client goals based on their needs")}
              className="px-2 py-1 text-xs bg-purple-100 text-purple-700 rounded hover:bg-purple-200"
            >
              🎯 Update Goals
            </button>
          </div>
        </div>
      )}

      {/* Section selector */}
      <div className="p-2 border-t border-gray-200 bg-gray-50 overflow-x-auto">
        <div className="flex space-x-2">
          {sectionOptions.map(option => (
            <button
              key={option.id}
              onClick={() => setSelectedSection(selectedSection === option.id ? '' : option.id)}
              className={`px-3 py-1 text-xs rounded-full whitespace-nowrap ${
                selectedSection === option.id
                  ? 'bg-primary-600 text-white'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
            >
              {option.label}
            </button>
          ))}
        </div>
      </div>
      
      <div className="p-4 border-t border-gray-200 bg-white">
        {/* Voice preview indicator */}
        {isVoiceInputActive && voicePreviewText && (
          <div className="mb-3 p-2 bg-blue-50 border border-blue-200 rounded-md">
            <div className="flex items-center text-sm text-blue-700">
              <svg className="w-4 h-4 mr-2 animate-pulse" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2a3 3 0 00-3 3v6a3 3 0 006 0V5a3 3 0 00-3-3z"/>
                <path d="M19 10v1a7 7 0 01-14 0v-1a1 1 0 012 0v1a5 5 0 0010 0v-1a1 1 0 012 0z"/>
              </svg>
              <span className="font-medium">Listening:</span>
              <span className="ml-2 italic">{voicePreviewText}</span>
            </div>
          </div>
        )}

        <div className="flex items-center">
          <div className="relative flex-grow">
            <input
              type="text"
              value={chatMessage}
              onChange={(e) => setChatMessage(e.target.value)}
              onKeyDown={(e) => e.key === 'Enter' && handleSendMessage()}
              placeholder={getPlaceholder()}
              className="w-full px-4 py-2 pr-12 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-primary-500"
              disabled={isSendingMessage}
            />
            <div className="absolute right-2 top-1/2 transform -translate-y-1/2">
              <VoiceInput
                onTranscription={handleVoiceTranscription}
                onTranscriptionUpdate={handleVoiceTranscriptionUpdate}
                disabled={isSendingMessage}
                className="flex items-center"
              />
            </div>
          </div>
          <button
            type="button"
            onClick={handleSendMessage}
            disabled={isSendingMessage || !chatMessage.trim()}
            className={`px-4 py-2 rounded-r-md focus:outline-none ${
              isSendingMessage || !chatMessage.trim()
                ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                : 'bg-primary-600 text-white hover:bg-primary-700'
            }`}
          >
            {isSendingMessage ? (
              <svg className="w-5 h-5 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
              </svg>
            ) : (
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
              </svg>
            )}
          </button>
        </div>
        
        {selectedSection && (
          <div className="mt-2 text-xs bg-blue-50 p-2 rounded-md text-blue-700">
            <span className="font-medium">Editing:</span> {sectionOptions.find(opt => opt.id === selectedSection)?.label}
            <button 
              onClick={() => setSelectedSection('')}
              className="ml-2 text-blue-500 hover:text-blue-700"
            >
              <svg className="w-3 h-3 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>
          </div>
        )}
        
        <p className="mt-2 text-xs text-gray-500">
          {selectedSection
            ? `The AI will help improve the ${sectionOptions.find(opt => opt.id === selectedSection)?.label.toLowerCase()} section of your proposal.`
            : 'Type your question or click the microphone to speak. Ask about packages, features, benefits, timeline, or other aspects of the proposal.'}
        </p>
      </div>
    </div>
  );
};

export default ProposalChatAssistant; 