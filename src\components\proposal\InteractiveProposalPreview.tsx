import React, { useState } from 'react';
import { EnhancedProposal } from '@/types/enhanced-proposal-schema';
import { DynamicProposalContent } from '@/lib/pydantic-ai-service';

interface InteractiveProposalPreviewProps {
  proposalData: EnhancedProposal;
  packages: any[];
  nextSteps: string[];
  formData: any;
  onEditSection: (section: string, data?: any) => void;
  scale: number;
  dynamicContent?: DynamicProposalContent;
}

const InteractiveProposalPreview: React.FC<InteractiveProposalPreviewProps> = ({
  proposalData,
  packages,
  nextSteps,
  formData,
  onEditSection,
  scale,
  dynamicContent
}) => {
  const [hoveredSection, setHoveredSection] = useState<string | null>(null);

  const handleSectionClick = (section: string, data?: any) => {
    onEditSection(section, data);
  };

  const getSectionClasses = (section: string) => {
    const baseClasses = "cursor-pointer transition-all duration-200 rounded-lg";
    const hoverClasses = hoveredSection === section 
      ? "ring-2 ring-blue-400 bg-blue-50" 
      : "hover:ring-2 hover:ring-blue-300 hover:bg-blue-25";
    return `${baseClasses} ${hoverClasses}`;
  };

  return (
    <div
      className="bg-white shadow-lg rounded-lg overflow-hidden mx-auto"
      style={{
        transform: `scale(${scale})`,
        transformOrigin: 'top left',
        width: `${Math.min(100 / scale, 120)}%`,
        maxWidth: 'none'
      }}
    >
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-600 to-blue-800 text-white p-6">
        <div className="text-center">
          <h1 className="text-3xl font-bold mb-2">GetFound Digital Growth Proposal</h1>
          <p className="text-blue-100">Professional Website & Digital Marketing Solutions</p>
        </div>
      </div>

      {/* Client Information - Clickable */}
      <div 
        className={`p-6 border-b border-gray-200 ${getSectionClasses('overview')}`}
        onClick={() => handleSectionClick('overview')}
        onMouseEnter={() => setHoveredSection('overview')}
        onMouseLeave={() => setHoveredSection(null)}
      >
        <div className="flex justify-between items-start">
          <div>
            <h2 className="text-xl font-semibold text-gray-900 mb-2">
              Proposal for {formData.businessName || 'Your Business'}
            </h2>
            <p className="text-gray-600">
              Client: {formData.clientName || 'Client Name'}
            </p>
            <p className="text-gray-600">
              Business Type: {formData.businessType || 'Business Type'}
            </p>
            <p className="text-gray-600">
              Reference: {formData.referenceNumber || 'REF-001'}
            </p>
          </div>
          <div className="text-right">
            <p className="text-sm text-gray-500">
              {new Date().toLocaleDateString()}
            </p>
            <span className="inline-block px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">
              {formData.status || 'Draft'}
            </span>
          </div>
        </div>
        
        {hoveredSection === 'overview' && (
          <div className="mt-2 text-sm text-blue-600 font-medium">
            ✏️ Click to edit client information and overview
          </div>
        )}
      </div>

      {/* Project Overview - Clickable */}
      <div
        className={`p-6 border-b border-gray-200 ${getSectionClasses('projectOverview')}`}
        onClick={() => handleSectionClick('projectOverview', proposalData)}
        onMouseEnter={() => setHoveredSection('projectOverview')}
        onMouseLeave={() => setHoveredSection(null)}
      >
        <h3 className="text-lg font-semibold text-gray-900 mb-3">Project Overview</h3>
        <div className="text-gray-700 leading-relaxed">
          {dynamicContent?.clientGreeting || proposalData.overview?.description || (
            <p>
              Our team at GetFound is excited to help transform {formData.businessName || 'your business'}'s online presence.
              As specialists in the {formData.businessType || 'local business'} industry, we understand the unique challenges
              you face in attracting and converting the right customers.
            </p>
          )}

          {dynamicContent?.situationAnalysis && (
            <p className="mt-3">
              {dynamicContent.situationAnalysis}
            </p>
          )}
        </div>

        {hoveredSection === 'projectOverview' && (
          <div className="mt-2 text-sm text-blue-600 font-medium">
            ✏️ Click to edit project overview
          </div>
        )}
      </div>

      {/* Client Goals - Clickable */}
      <div
        className={`p-6 border-b border-gray-200 ${getSectionClasses('clientGoals')}`}
        onClick={() => handleSectionClick('clientGoals', proposalData)}
        onMouseEnter={() => setHoveredSection('clientGoals')}
        onMouseLeave={() => setHoveredSection(null)}
      >
        <h3 className="text-lg font-semibold text-gray-900 mb-3">
          {proposalData.clientGoals?.title || "Your Business Goals"}
        </h3>

        {proposalData.clientGoals?.description && (
          <p className="text-gray-600 mb-4">{proposalData.clientGoals.description}</p>
        )}

        <div className="space-y-3">
          {proposalData.clientGoals?.goals && proposalData.clientGoals.goals.length > 0 ? (
            proposalData.clientGoals.goals.map((goal: string, index: number) => (
              <div key={index} className="flex items-start">
                <div className="flex-shrink-0 w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3"></div>
                <div>
                  <p className="text-gray-700">{goal}</p>
                </div>
              </div>
            ))
          ) : (
            <div className="space-y-3">
              <div className="flex items-start">
                <div className="flex-shrink-0 w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3"></div>
                <div>
                  <h4 className="font-medium text-gray-900">Professional Online Credibility</h4>
                  <p className="text-gray-600 text-sm">Establish a website that positions you as a trustworthy, established provider in your market.</p>
                </div>
              </div>
              <div className="flex items-start">
                <div className="flex-shrink-0 w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3"></div>
                <div>
                  <h4 className="font-medium text-gray-900">Targeted Lead Generation</h4>
                  <p className="text-gray-600 text-sm">Attract more qualified leads who are specifically looking for your core services.</p>
                </div>
              </div>
            </div>
          )}
        </div>
        
        {hoveredSection === 'clientGoals' && (
          <div className="mt-2 text-sm text-blue-600 font-medium">
            ✏️ Click to edit client goals
          </div>
        )}
      </div>

      {/* Packages - Clickable */}
      <div 
        className={`p-6 border-b border-gray-200 ${getSectionClasses('packages')}`}
        onClick={() => handleSectionClick('packages')}
        onMouseEnter={() => setHoveredSection('packages')}
        onMouseLeave={() => setHoveredSection(null)}
      >
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Recommended Solutions</h3>
        <div className="space-y-4">
          {packages.map((pkg, index) => (
            <div key={index} className="border border-gray-200 rounded-lg p-4 relative">
              {pkg.isRecommended && (
                <div className="absolute -top-2 left-4 bg-blue-600 text-white px-2 py-1 text-xs rounded">
                  Recommended
                </div>
              )}
              <div className="flex justify-between items-start mb-3">
                <h4 className="text-lg font-semibold text-gray-900">{pkg.name}</h4>
                <div className="text-right">
                  <div className="text-lg font-bold text-blue-600">{pkg.setupFee}</div>
                  <div className="text-sm text-gray-600">Setup Fee</div>
                  <div className="text-lg font-bold text-green-600">{pkg.monthlyFee}</div>
                  <div className="text-sm text-gray-600">Monthly</div>
                </div>
              </div>
              <ul className="space-y-2">
                {pkg.features.filter(feature => feature && feature.trim()).map((feature, featureIndex) => (
                  <li key={featureIndex} className="flex items-start">
                    <svg className="w-4 h-4 text-green-500 mt-0.5 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    <span className="text-gray-700 text-sm">{feature}</span>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>
        
        {hoveredSection === 'packages' && (
          <div className="mt-2 text-sm text-blue-600 font-medium">
            ✏️ Click to edit packages and pricing
          </div>
        )}
      </div>

      {/* Next Steps - Clickable */}
      <div 
        className={`p-6 border-b border-gray-200 ${getSectionClasses('nextSteps')}`}
        onClick={() => handleSectionClick('nextSteps')}
        onMouseEnter={() => setHoveredSection('nextSteps')}
        onMouseLeave={() => setHoveredSection(null)}
      >
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Next Steps</h3>
        <div className="space-y-3">
          {nextSteps.map((step, index) => (
            <div key={index} className="flex items-start">
              <div className="flex-shrink-0 w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-medium mr-3">
                {index + 1}
              </div>
              <p className="text-gray-700">{step}</p>
            </div>
          ))}
        </div>
        
        {hoveredSection === 'nextSteps' && (
          <div className="mt-2 text-sm text-blue-600 font-medium">
            ✏️ Click to edit next steps
          </div>
        )}
      </div>

      {/* Footer */}
      <div className="bg-gray-50 p-6 text-center">
        <p className="text-gray-600 text-sm">
          Thank you for considering GetFound for your digital growth needs.
        </p>
        <p className="text-gray-600 text-sm mt-1">
          We look forward to helping {formData.businessName || 'your business'} succeed online.
        </p>
      </div>
    </div>
  );
};

export default InteractiveProposalPreview;
