import React, { useState } from 'react';
import { EnhancedProposal } from '@/types/enhanced-proposal-schema';
import { DynamicProposalContent } from '@/lib/pydantic-ai-service';

interface InteractiveProposalPreviewProps {
  proposalData: EnhancedProposal;
  packages: any[];
  nextSteps: string[];
  formData: any;
  onEditSection: (section: string, data?: any) => void;
  scale: number;
  dynamicContent?: DynamicProposalContent;
}

const InteractiveProposalPreview: React.FC<InteractiveProposalPreviewProps> = ({
  proposalData,
  packages,
  nextSteps,
  formData,
  onEditSection,
  scale,
  dynamicContent
}) => {
  const [hoveredSection, setHoveredSection] = useState<string | null>(null);

  const handleSectionClick = (section: string, data?: any) => {
    onEditSection(section, data);
  };

  const getSectionClasses = (section: string) => {
    const baseClasses = "cursor-pointer transition-all duration-200 rounded-lg";
    const hoverClasses = hoveredSection === section 
      ? "ring-2 ring-blue-400 bg-blue-50" 
      : "hover:ring-2 hover:ring-blue-300 hover:bg-blue-25";
    return `${baseClasses} ${hoverClasses}`;
  };

  return (
    <div
      className="bg-white shadow-lg rounded-lg overflow-hidden mx-auto"
      style={{
        transform: `scale(${scale})`,
        transformOrigin: 'top left',
        width: `${Math.min(100 / scale, 120)}%`,
        maxWidth: 'none'
      }}
    >
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-600 to-blue-800 text-white p-6">
        <div className="text-center">
          <h1 className="text-3xl font-bold mb-2">GetFound Digital Growth Proposal</h1>
          <p className="text-blue-100">Professional Website & Digital Marketing Solutions</p>
        </div>
      </div>

      {/* Client Information - Clickable */}
      <div 
        className={`p-6 border-b border-gray-200 ${getSectionClasses('overview')}`}
        onClick={() => handleSectionClick('overview')}
        onMouseEnter={() => setHoveredSection('overview')}
        onMouseLeave={() => setHoveredSection(null)}
      >
        <div className="flex justify-between items-start">
          <div>
            <h2 className="text-xl font-semibold text-gray-900 mb-2">
              Proposal for {formData.businessName || 'Your Business'}
            </h2>
            <p className="text-gray-600">
              Client: {formData.clientName || 'Client Name'}
            </p>
            <p className="text-gray-600">
              Business Type: {formData.businessType || 'Business Type'}
            </p>
            <p className="text-gray-600">
              Reference: {formData.referenceNumber || 'REF-001'}
            </p>
          </div>
          <div className="text-right">
            <p className="text-sm text-gray-500">
              {new Date().toLocaleDateString()}
            </p>
            <span className="inline-block px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">
              {formData.status || 'Draft'}
            </span>
          </div>
        </div>
        
        {hoveredSection === 'overview' && (
          <div className="mt-2 text-sm text-blue-600 font-medium">
            ✏️ Click to edit client information and overview
          </div>
        )}
      </div>

      {/* Project Overview - Clickable */}
      <div
        className={`p-6 border-b border-gray-200 ${getSectionClasses('projectOverview')}`}
        onClick={() => handleSectionClick('projectOverview', proposalData)}
        onMouseEnter={() => setHoveredSection('projectOverview')}
        onMouseLeave={() => setHoveredSection(null)}
      >
        <h3 className="text-lg font-semibold text-gray-900 mb-3">Project Overview</h3>
        <div className="text-gray-700 leading-relaxed">
          {dynamicContent?.clientGreeting || proposalData.overview?.description || (
            <p>
              Our team at GetFound is excited to help transform {formData.businessName || 'your business'}'s online presence.
              As specialists in the {formData.businessType || 'local business'} industry, we understand the unique challenges
              you face in attracting and converting the right customers.
            </p>
          )}

          {dynamicContent?.situationAnalysis && (
            <p className="mt-3">
              {dynamicContent.situationAnalysis}
            </p>
          )}
        </div>

        {hoveredSection === 'projectOverview' && (
          <div className="mt-2 text-sm text-blue-600 font-medium">
            ✏️ Click to edit project overview
          </div>
        )}
      </div>

      {/* Client Goals - Clickable */}
      <div
        className={`p-6 border-b border-gray-200 ${getSectionClasses('clientGoals')}`}
        onClick={() => handleSectionClick('clientGoals', proposalData)}
        onMouseEnter={() => setHoveredSection('clientGoals')}
        onMouseLeave={() => setHoveredSection(null)}
      >
        <h3 className="text-lg font-semibold text-gray-900 mb-3">
          {proposalData.clientGoals?.title || "Your Business Goals"}
        </h3>

        {proposalData.clientGoals?.description && (
          <p className="text-gray-600 mb-4">{proposalData.clientGoals.description}</p>
        )}

        <div className="space-y-3">
          {proposalData.clientGoals?.goals && proposalData.clientGoals.goals.length > 0 ? (
            proposalData.clientGoals.goals.map((goal: string, index: number) => (
              <div key={index} className="flex items-start">
                <div className="flex-shrink-0 w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3"></div>
                <div>
                  <p className="text-gray-700">{goal}</p>
                </div>
              </div>
            ))
          ) : (
            <div className="space-y-3">
              <div className="flex items-start">
                <div className="flex-shrink-0 w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3"></div>
                <div>
                  <h4 className="font-medium text-gray-900">Professional Online Credibility</h4>
                  <p className="text-gray-600 text-sm">Establish a website that positions you as a trustworthy, established provider in your market.</p>
                </div>
              </div>
              <div className="flex items-start">
                <div className="flex-shrink-0 w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3"></div>
                <div>
                  <h4 className="font-medium text-gray-900">Targeted Lead Generation</h4>
                  <p className="text-gray-600 text-sm">Attract more qualified leads who are specifically looking for your core services.</p>
                </div>
              </div>
            </div>
          )}
        </div>
        
        {hoveredSection === 'clientGoals' && (
          <div className="mt-2 text-sm text-blue-600 font-medium">
            ✏️ Click to edit client goals
          </div>
        )}
      </div>

      {/* Packages - Clickable */}
      <div
        className={`p-6 border-b border-gray-200 ${getSectionClasses('packages')}`}
        onClick={() => handleSectionClick('packages')}
        onMouseEnter={() => setHoveredSection('packages')}
        onMouseLeave={() => setHoveredSection(null)}
      >
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Package Options</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {packages.map((pkg, index) => {
            const isRecommended = pkg.isRecommended ||
              (proposalData.aiRecommendation?.recommendedPackage === 'premium' && pkg.name.toLowerCase().includes('premium')) ||
              (proposalData.aiRecommendation?.recommendedPackage === 'basic' && pkg.name.toLowerCase().includes('basic'));

            return (
              <div key={index} className={`border rounded-xl p-6 relative ${isRecommended ? 'border-primary-500 bg-primary-50' : 'border-gray-200 bg-white'}`}>
                {isRecommended && (
                  <div className="absolute -top-3 left-4 px-3 py-1 bg-primary-500 text-white text-xs rounded-full font-medium">
                    RECOMMENDED
                  </div>
                )}
                <div className="mb-4">
                  <h4 className="text-lg font-semibold text-gray-900 mb-2">{pkg.name}</h4>
                  <div className="flex items-baseline">
                    <span className="text-3xl font-bold text-gray-900">{pkg.setupFee}</span>
                    <span className="text-gray-600 ml-2">setup</span>
                  </div>
                  <div className="flex items-baseline mt-1">
                    <span className="text-xl font-semibold text-primary-600">{pkg.monthlyFee}</span>
                    <span className="text-gray-600 ml-1">/month</span>
                  </div>
                </div>
                <ul className="space-y-3">
                  {pkg.features.filter(feature => feature && feature.trim()).map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-start">
                      <div className="flex-shrink-0 h-5 w-5 rounded-full bg-green-100 text-green-600 flex items-center justify-center mr-3 mt-0.5">
                        <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                      </div>
                      <span className="text-gray-700 text-sm">{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>
            );
          })}
        </div>

        {hoveredSection === 'packages' && (
          <div className="mt-2 text-sm text-blue-600 font-medium">
            ✏️ Click to edit packages and pricing
          </div>
        )}
      </div>

      {/* AI Recommendation Section */}
      {proposalData?.aiRecommendation && (
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-xl p-8 mb-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
            <svg className="w-5 h-5 mr-2 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
            </svg>
            {proposalData.aiRecommendation.title}
          </h2>

          <div className="bg-white rounded-lg p-6 shadow-sm">
            <div className="flex items-center justify-between mb-4">
              <div>
                <h3 className="text-lg font-medium text-gray-900">
                  Recommended: <span className="text-primary-600">{proposalData.aiRecommendation.recommendedPackage === 'premium' ? 'Premium Package' : 'Basic Package'}</span>
                </h3>
                <div className="flex items-center mt-1">
                  <span className="text-sm text-gray-600 mr-2">Confidence:</span>
                  <div className="flex items-center">
                    <div className="w-24 bg-gray-200 rounded-full h-2 mr-2">
                      <div
                        className="bg-green-500 h-2 rounded-full"
                        style={{ width: `${(proposalData.aiRecommendation.confidence || 0) * 100}%` }}
                      ></div>
                    </div>
                    <span className="text-sm font-medium text-green-600">
                      {Math.round((proposalData.aiRecommendation.confidence || 0) * 100)}%
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-2">Why we recommend this package:</h4>
              <ul className="space-y-2">
                {proposalData.aiRecommendation.reasoning?.map((reason: string, index: number) => (
                  <li key={index} className="flex items-start">
                    <div className="flex-shrink-0 h-5 w-5 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center mr-2 mt-0.5">
                      <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                      </svg>
                    </div>
                    <span className="text-gray-700 text-sm">{reason}</span>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      )}

      {/* Payment Options */}
      <div className="bg-white shadow-md rounded-xl p-8 mb-8">
        <h2 className="text-xl font-semibold text-gray-900 mb-6 flex items-center">
          <svg className="w-5 h-5 mr-2 text-primary-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
          Investment & Payment Options
        </h2>

        {/* Package Choice Section */}
        <div className="mb-8">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
            <h3 className="text-lg font-medium text-gray-900 mb-3 flex items-center">
              <svg className="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
              Package Selection & Flexibility
            </h3>
            <p className="text-gray-700 mb-4">
              Based on our analysis, we recommend the <strong>Premium Package</strong> for {formData.businessName || 'your business'} to fully support your growth goals. However, you have complete flexibility to choose the package that best fits your current needs and budget.
            </p>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Recommended Package Highlight */}
              {(() => {
                const recommendedPkg = packages.find((pkg: any) =>
                  pkg.isRecommended ||
                  (proposalData?.aiRecommendation?.recommendedPackage === 'premium' && pkg.name.toLowerCase().includes('premium')) ||
                  (proposalData?.aiRecommendation?.recommendedPackage === 'basic' && pkg.name.toLowerCase().includes('basic'))
                );
                const alternativePkg = packages.find((pkg: any) => pkg !== recommendedPkg);

                return (
                  <>
                    {recommendedPkg && (
                      <div className="bg-white border-2 border-primary-500 rounded-lg p-4">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="font-medium text-gray-900">{recommendedPkg.name}</h4>
                          <span className="px-2 py-1 bg-primary-500 text-white text-xs rounded-full">RECOMMENDED</span>
                        </div>
                        <div className="text-sm text-gray-600 mb-2">
                          <span className="font-medium text-primary-600">{recommendedPkg.setupFee}</span> setup + <span className="font-medium text-primary-600">{recommendedPkg.monthlyFee}</span>/month
                        </div>
                        <p className="text-xs text-gray-600">
                          Includes all features needed for {formData.businessType || 'your business type'} growth and lead generation.
                        </p>
                      </div>
                    )}

                    {alternativePkg && (
                      <div className="bg-white border border-gray-300 rounded-lg p-4">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="font-medium text-gray-900">{alternativePkg.name}</h4>
                          <span className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full">ALTERNATIVE</span>
                        </div>
                        <div className="text-sm text-gray-600 mb-2">
                          <span className="font-medium text-gray-700">{alternativePkg.setupFee}</span> setup + <span className="font-medium text-gray-700">{alternativePkg.monthlyFee}</span>/month
                        </div>
                        <p className="text-xs text-gray-600">
                          Good starting point with upgrade option available anytime.
                        </p>
                      </div>
                    )}
                  </>
                );
              })()}
            </div>

            <div className="mt-4 p-3 bg-amber-50 border border-amber-200 rounded-lg">
              <p className="text-sm text-amber-800">
                <strong>Upgrade Anytime:</strong> Start with any package and upgrade when you're ready. We'll credit your existing setup investment toward the higher package.
              </p>
            </div>
          </div>
        </div>

        {/* Payment Method Options */}
        <div>
          <h3 className="text-lg font-medium text-gray-900 mb-4">Choose Your Payment Method</h3>
          <p className="text-gray-600 mb-6">Select the payment structure that works best for your cash flow, regardless of which package you choose.</p>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="border border-gray-200 rounded-xl p-6 bg-gray-50 hover:bg-white hover:shadow-md transition-all">
              <h4 className="font-medium text-gray-900 mb-3">Standard Payment</h4>
              <p className="text-gray-700 text-sm mb-4">
                Pay setup fee upfront, then monthly subscription. Lower total cost.
              </p>

              <div className="space-y-3">
                {packages.map((pkg: any, index: number) => {
                  const setupFeeNum = parseFloat(pkg.setupFee.replace(/[$,]/g, ''));
                  const monthlyFeeNum = parseFloat(pkg.monthlyFee.replace(/[$,]/g, ''));

                  return (
                    <div key={index} className="border border-gray-200 rounded-lg p-3 bg-white">
                      <div className="flex justify-between items-center mb-2">
                        <span className="font-medium text-sm">{pkg.name}</span>
                      </div>
                      <div className="space-y-1 text-sm">
                        <div className="flex justify-between">
                          <span>Initial payment:</span>
                          <span className="font-medium">{pkg.setupFee}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Monthly thereafter:</span>
                          <span className="font-medium">{pkg.monthlyFee}/month</span>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>

            <div className="border-2 border-primary-500 rounded-xl p-6 bg-white shadow-md relative">
              <div className="absolute -top-3 left-4 px-3 py-1 bg-primary-500 text-white text-xs rounded-full font-medium">
                POPULAR CHOICE
              </div>

              <h4 className="font-medium text-gray-900 mb-3">Amortized Setup Fee</h4>
              <p className="text-gray-700 text-sm mb-4">
                Spread setup fee over 12 months. Better cash flow, no interest.
              </p>

              <div className="space-y-3">
                {packages.map((pkg: any, index: number) => {
                  const setupFeeNum = parseFloat(pkg.setupFee.replace(/[$,]/g, ''));
                  const monthlyFeeNum = parseFloat(pkg.monthlyFee.replace(/[$,]/g, ''));
                  const amortizedSetupFee = setupFeeNum / 12;
                  const totalAmortizedMonthly = amortizedSetupFee + monthlyFeeNum;

                  return (
                    <div key={index} className="border border-gray-200 rounded-lg p-3 bg-gray-50">
                      <div className="flex justify-between items-center mb-2">
                        <span className="font-medium text-sm">{pkg.name}</span>
                      </div>
                      <div className="space-y-1 text-sm">
                        <div className="flex justify-between">
                          <span>Months 1-12:</span>
                          <span className="font-medium">${totalAmortizedMonthly.toFixed(2)}/month</span>
                        </div>
                        <div className="flex justify-between text-xs text-gray-600">
                          <span className="ml-2">• Service: ${monthlyFeeNum.toFixed(2)}</span>
                          <span>• Setup: ${amortizedSetupFee.toFixed(2)}</span>
                        </div>
                        <div className="flex justify-between border-t border-gray-200 pt-1">
                          <span>Month 13+:</span>
                          <span className="font-medium">{pkg.monthlyFee}/month</span>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* AI-Generated Questions & Answers */}
      {proposalData?.specificQuestions && proposalData.specificQuestions.questions && proposalData.specificQuestions.questions.length > 0 && (
        <div className="bg-white shadow-md rounded-xl p-8 mb-8 border-l-4 border-green-500">
          <h2 className="text-xl font-semibold text-gray-900 mb-6 flex items-center">
            <svg className="w-5 h-5 mr-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            {proposalData.specificQuestions.title}
          </h2>

          <div className="space-y-6">
            {proposalData.specificQuestions.questions.map((qa: any, index: number) => (
              <div key={index} className="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
                <div className="flex items-start">
                  <div className="flex-shrink-0 h-8 w-8 rounded-full bg-green-100 text-green-600 flex items-center justify-center mr-4 mt-1">
                    <span className="text-sm font-semibold">Q</span>
                  </div>
                  <div className="flex-1">
                    <h3 className="text-lg font-medium text-gray-900 mb-3">{qa.question}</h3>
                    <div className="flex items-start">
                      <div className="flex-shrink-0 h-8 w-8 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center mr-4 mt-1">
                        <span className="text-sm font-semibold">A</span>
                      </div>
                      <div className="flex-1">
                        <p className="text-gray-700 leading-relaxed">{qa.answer}</p>
                        {qa.category && (
                          <span className="inline-block mt-3 px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full">
                            {qa.category.charAt(0).toUpperCase() + qa.category.slice(1)}
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Add-on Services */}
      {proposalData?.addons && proposalData.addons.items && proposalData.addons.items.length > 0 && (
        <div className="bg-white shadow-md rounded-xl p-8 mb-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-6 flex items-center">
            <svg className="w-5 h-5 mr-2 text-primary-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v3m0 0v3m0-3h3m-3 0H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            {proposalData.addons.title || "Optional Add-on Services"}
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {proposalData.addons.items.map((addon: any, index: number) => (
              <div key={index} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                <h3 className="font-medium text-gray-900 mb-2">{addon.name}</h3>
                <p className="text-gray-600 text-sm mb-3">{addon.description}</p>
                <div className="flex justify-between items-center">
                  <span className="text-lg font-semibold text-primary-600">{addon.price}</span>
                  {addon.popular && (
                    <span className="px-2 py-1 bg-primary-100 text-primary-600 text-xs rounded-full">Popular</span>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Custom Sections */}
      {proposalData?.customSections && proposalData.customSections.length > 0 &&
        proposalData.customSections.map((section: any, index: number) => (
          <div key={index} className="bg-white shadow-md rounded-xl p-8 mb-8">
            <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
              <svg className="w-5 h-5 mr-2 text-primary-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
              </svg>
              {section.title}
            </h2>
            <div className="prose max-w-none">
              <p className="text-gray-700 whitespace-pre-line">{section.content}</p>
            </div>
          </div>
        ))
      }

      {/* Next Steps - Clickable */}
      <div
        className={`p-6 border-b border-gray-200 ${getSectionClasses('nextSteps')}`}
        onClick={() => handleSectionClick('nextSteps')}
        onMouseEnter={() => setHoveredSection('nextSteps')}
        onMouseLeave={() => setHoveredSection(null)}
      >
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Next Steps</h3>
        <div className="space-y-3">
          {nextSteps.map((step, index) => (
            <div key={index} className="flex items-start">
              <div className="flex-shrink-0 w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-medium mr-3">
                {index + 1}
              </div>
              <p className="text-gray-700">{step}</p>
            </div>
          ))}
        </div>

        {hoveredSection === 'nextSteps' && (
          <div className="mt-2 text-sm text-blue-600 font-medium">
            ✏️ Click to edit next steps
          </div>
        )}
      </div>

      {/* Footer */}
      <div className="bg-gray-50 p-6 text-center">
        <p className="text-gray-600 text-sm">
          Thank you for considering GetFound for your digital growth needs.
        </p>
        <p className="text-gray-600 text-sm mt-1">
          We look forward to helping {formData.businessName || 'your business'} succeed online.
        </p>
      </div>
    </div>
  );
};

export default InteractiveProposalPreview;
