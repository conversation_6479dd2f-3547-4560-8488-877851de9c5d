Metadata-Version: 2.3
Name: logfire
Version: 0.28.0
Summary: The best Python observability tool! 🪵🔥
Author-email: Pydantic Team <<EMAIL>>, <PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>, <PERSON> <ad<PERSON>@pydantic.dev>, <PERSON> <<EMAIL>>, <PERSON><PERSON> <ma<PERSON><PERSON>@pydantic.dev>, <PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>
License-Expression: MIT
License-File: LICENSE
Classifier: Development Status :: 4 - Beta
Classifier: Environment :: Console
Classifier: Environment :: MacOS X
Classifier: Intended Audience :: Developers
Classifier: Intended Audience :: Information Technology
Classifier: Intended Audience :: System Administrators
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: POSIX :: Linux
Classifier: Operating System :: Unix
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3 :: Only
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Topic :: Internet
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Requires-Dist: opentelemetry-exporter-otlp-proto-http>=1.21.0
Requires-Dist: opentelemetry-instrumentation>=0.41b0
Requires-Dist: opentelemetry-sdk>=1.21.0
Requires-Dist: protobuf>=4.23.4
Requires-Dist: rich>=13.4.2
Requires-Dist: tomli>=2.0.1; python_version < '3.11'
Requires-Dist: typing-extensions>=4.0.0
Provides-Extra: aiohttp
Requires-Dist: opentelemetry-instrumentation-aiohttp-client>=0.42b0; extra == 'aiohttp'
Provides-Extra: asyncpg
Requires-Dist: opentelemetry-instrumentation-asyncpg>=0.42b0; extra == 'asyncpg'
Provides-Extra: celery
Requires-Dist: opentelemetry-instrumentation-celery>=0.42b0; extra == 'celery'
Provides-Extra: django
Requires-Dist: opentelemetry-instrumentation-django>=0.42b0; extra == 'django'
Provides-Extra: fastapi
Requires-Dist: opentelemetry-instrumentation-fastapi>=0.42b0; extra == 'fastapi'
Provides-Extra: flask
Requires-Dist: opentelemetry-instrumentation-flask>=0.42b0; extra == 'flask'
Provides-Extra: httpx
Requires-Dist: opentelemetry-instrumentation-httpx>=0.42b0; extra == 'httpx'
Provides-Extra: psycopg
Requires-Dist: opentelemetry-instrumentation-psycopg2>=0.42b0; extra == 'psycopg'
Requires-Dist: packaging; extra == 'psycopg'
Provides-Extra: psycopg2
Requires-Dist: opentelemetry-instrumentation-psycopg2>=0.42b0; extra == 'psycopg2'
Requires-Dist: packaging; extra == 'psycopg2'
Provides-Extra: pymongo
Requires-Dist: opentelemetry-instrumentation-pymongo>=0.42b0; extra == 'pymongo'
Provides-Extra: redis
Requires-Dist: opentelemetry-instrumentation-redis>=0.42b0; extra == 'redis'
Provides-Extra: requests
Requires-Dist: opentelemetry-instrumentation-requests>=0.42b0; extra == 'requests'
Provides-Extra: sqlalchemy
Requires-Dist: opentelemetry-instrumentation-sqlalchemy>=0.42b0; extra == 'sqlalchemy'
Provides-Extra: starlette
Requires-Dist: opentelemetry-instrumentation-starlette>=0.42b0; extra == 'starlette'
Provides-Extra: system-metrics
Requires-Dist: opentelemetry-instrumentation-system-metrics>=0.42b0; extra == 'system-metrics'
Description-Content-Type: text/markdown

# Pydantic Logfire — Uncomplicated Observability

[![CI](https://github.com/pydantic/logfire/actions/workflows/main.yml/badge.svg?event=push)](https://github.com/pydantic/logfire/actions?query=event%3Apush+branch%3Amain+workflow%3ACI)
[![codecov](https://codecov.io/gh/pydantic/logfire/graph/badge.svg?token=735CNGCGFD)](https://codecov.io/gh/pydantic/logfire)
[![pypi](https://img.shields.io/pypi/v/logfire.svg)](https://pypi.python.org/pypi/logfire)
[![license](https://img.shields.io/github/license/pydantic/logfire.svg)](https://github.com/pydantic/logfire/blob/main/LICENSE)
[![versions](https://img.shields.io/pypi/pyversions/logfire.svg)](https://github.com/pydantic/logfire)

See the [documentation](https://docs.pydantic.dev/logfire/) for more information.

**Feel free to report issues and ask any questions about Logfire in this repository!**

This repo contains the Python SDK for `logfire` and documentation; the server application for recording and displaying data is closed source.

## Using Logfire

This is a very brief overview of how to use Logfire, the [documentation](https://docs.pydantic.dev/logfire/) has much more detail.

### Install

```bash
pip install logfire
```
[_(learn more)_](https://docs.pydantic.dev/logfire/guides/first_steps/#install)

## Authenticate

```bash
logfire auth
```
[_(learn more)_](https://docs.pydantic.dev/logfire/guides/first_steps/#authentication)

### Manual tracing

Here's a simple manual tracing (aka logging) example:

```python
import logfire
from datetime import date

logfire.info('Hello, {name}!', name='world')

with logfire.span('Asking the user their {question}', question='age'):
    user_input = input('How old are you [YYYY-mm-dd]? ')
    dob = date.fromisoformat(user_input)
    logfire.debug('{dob=} {age=!r}', dob=dob, age=date.today() - dob)
```
[_(learn more)_](https://docs.pydantic.dev/logfire/guides/onboarding_checklist/03_add_manual_tracing/)

### Integration

Or you can also avoid manual instrumentation and instead integrate with [lots of popular packages](https://docs.pydantic.dev/logfire/integrations/), here's an example of integrating with FastAPI:

```py
import logfire
from pydantic import BaseModel
from fastapi import FastAPI

app = FastAPI()

logfire.configure()
logfire.instrument_fastapi(app)
# next, instrument your database connector, http library etc. and add the logging handler

class User(BaseModel):
    name: str
    country_code: str

@app.post('/')
async def add_user(user: User):
    # we would store the user here
    return {'message': f'{user.name} added'}
```
[_(learn more)_](https://docs.pydantic.dev/logfire/integrations/fastapi/)

Logfire gives you a view into how your code is running like this:

![Logfire screenshot](https://docs.pydantic.dev/logfire/images/index/logfire-screenshot-fastapi-200.png)

## Contributing

We'd love anyone interested to contribute to the Logfire SDK and documentation, see the [contributing guide](./CONTRIBUTING.md).

## Reporting a Security Vulnerability

See our [security policy](https://github.com/pydantic/logfire/security).
