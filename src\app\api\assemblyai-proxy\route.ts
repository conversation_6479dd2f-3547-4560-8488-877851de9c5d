import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    // AssemblyAI real-time streaming requires a paid account
    // For demo purposes, return a mock configuration that simulates voice input
    console.log('AssemblyAI proxy called - real-time streaming requires paid account');
    console.log('Returning demo configuration for voice input testing');

    // Return demo configuration that allows testing the voice input UI
    return NextResponse.json({
      token: 'demo-token',
      websocketUrl: 'wss://echo.websocket.org', // Echo WebSocket for testing
      sampleRate: 16000,
      apiVersion: 'demo',
      note: 'Demo mode: AssemblyAI real-time streaming requires a paid account. Add a credit card at https://app.assemblyai.com/ to use real speech-to-text.'
    });
  } catch (error) {
    console.error('Error in AssemblyAI proxy:', error);
    return NextResponse.json(
      { error: 'Failed to get AssemblyAI connection info' },
      { status: 500 }
    );
  }
}
