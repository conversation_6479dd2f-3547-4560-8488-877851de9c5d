import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    // For development/testing purposes, return mock configuration
    // In production, you would use a valid AssemblyAI API key
    console.log('AssemblyAI proxy called - returning mock configuration for testing');

    // Return mock configuration that allows testing the voice input UI
    return NextResponse.json({
      token: 'mock-token-for-testing',
      websocketUrl: 'wss://echo.websocket.org', // Echo WebSocket for testing
      sampleRate: 16000,
      apiVersion: 'mock',
      note: 'Using mock configuration for testing. Replace with real AssemblyAI credentials for production.'
    });
  } catch (error) {
    console.error('Error in AssemblyAI proxy:', error);
    return NextResponse.json(
      { error: 'Failed to get AssemblyAI connection info' },
      { status: 500 }
    );
  }
}
