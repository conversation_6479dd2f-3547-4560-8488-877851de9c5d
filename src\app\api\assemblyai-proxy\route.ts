import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    // Generate a temporary token for AssemblyAI real-time streaming
    const API_KEY = 'e418098cc7d44675804de163364909b0';

    const response = await fetch('https://api.assemblyai.com/v2/realtime/token', {
      method: 'POST',
      headers: {
        'Authorization': API_KEY,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        expires_in: 3600 // Token expires in 1 hour
      })
    });

    if (!response.ok) {
      throw new Error(`AssemblyAI token request failed: ${response.status}`);
    }

    const tokenData = await response.json();

    return NextResponse.json({
      token: tokenData.token,
      websocketUrl: `wss://api.assemblyai.com/v2/realtime/ws?sample_rate=16000&token=${tokenData.token}`
    });
  } catch (error) {
    console.error('Error generating AssemblyAI token:', error);
    return NextResponse.json(
      { error: 'Failed to generate AssemblyAI token' },
      { status: 500 }
    );
  }
}
