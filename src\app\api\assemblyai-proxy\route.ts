import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    // Generate a temporary token for AssemblyAI real-time streaming (v2 API)
    // Now using paid account with real streaming
    const API_KEY = '62bf5db329bf42c7b5b64081d128499a';

    console.log('Generating AssemblyAI real-time streaming token...');

    const response = await fetch('https://api.assemblyai.com/v2/realtime/token', {
      method: 'POST',
      headers: {
        'Authorization': API_KEY,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        expires_in: 3600 // Token expires in 1 hour
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('AssemblyAI token request failed:', response.status, errorText);
      throw new Error(`AssemblyAI token request failed: ${response.status} - ${errorText}`);
    }

    const tokenData = await response.json();
    console.log('AssemblyAI token generated successfully');

    return NextResponse.json({
      token: tokenData.token,
      websocketUrl: `wss://api.assemblyai.com/v2/realtime/ws?sample_rate=16000&token=${tokenData.token}`,
      sampleRate: 16000,
      apiVersion: 'v2'
    });
  } catch (error) {
    console.error('Error generating AssemblyAI token:', error);
    return NextResponse.json(
      { error: 'Failed to generate AssemblyAI token', details: error.message },
      { status: 500 }
    );
  }
}
