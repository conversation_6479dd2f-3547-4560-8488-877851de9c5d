import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const { message } = await request.json();
    
    console.log('Voice test message received:', message);
    
    return NextResponse.json({
      success: true,
      message: 'Voice message received successfully',
      receivedMessage: message,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error in voice test endpoint:', error);
    return NextResponse.json(
      { error: 'Failed to process voice message' },
      { status: 500 }
    );
  }
}
