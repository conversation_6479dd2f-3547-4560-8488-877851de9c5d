/**
 * Universal Dynamic Proposal Content Generator
 * Works for ANY local business - dentists, lawyers, landscapers, restaurants, contractors, etc.
 */

export interface CustomerAnalysis {
  businessName: string;
  currentSituation: string;
  keyNeeds: string[];
  recommendedPackage: 'basic' | 'premium';
  confidence: number;
  reasoning: string[];
}

export interface CustomerQuestion {
  question: string;
  answer: string;
  category: 'website' | 'google' | 'goals' | 'pricing' | 'process' | 'general';
}

export interface DynamicProposalContent {
  projectTitle: string;
  acknowledgment: string;
  packageRecommendation: {
    recommended: 'basic' | 'premium';
    reasoning: string;
    whyThisPackage: string[];
  };
  questionsAndAnswers: CustomerQuestion[];
  nextSteps: string[];
}

/**
 * Analyzes customer information and generates dynamic proposal content
 */
export function generateDynamicProposal(customerInfo: string): DynamicProposalContent {
  const analysis = analyzeCustomerInfo(customerInfo);

  return {
    projectTitle: `Modern Website & Digital Marketing Solution for ${analysis.businessName}`,

    clientGreeting: generateClientGreeting(analysis),

    situationAnalysis: generateSituationAnalysis(analysis),

    goalsSection: generateGoalsSection(analysis),

    packageRecommendation: {
      recommended: analysis.recommendedPackage,
      reasoning: analysis.reasoning.join(' '),
      whyThisPackage: generatePackageReasons(analysis)
    },

    customizedFeatures: {
      basic: generateBasicFeatures(analysis),
      premium: generatePremiumFeatures(analysis)
    },

    specificSolutions: generateSpecificSolutions(analysis),

    questionsAndAnswers: extractQuestionsAndAnswers(customerInfo, analysis),

    nextSteps: generateNextSteps(analysis)
  };
}

/**
 * Analyzes customer information to understand their needs
 */
function analyzeCustomerInfo(customerInfo: string): CustomerAnalysis {
  const info = customerInfo.toLowerCase();
  
  // Extract business details
  const businessName = extractBusinessName(customerInfo);
  const businessType = extractBusinessType(info);
  
  // Analyze current situation
  const currentSituation = analyzeSituation(info);
  
  // Extract target markets
  const targetMarkets = extractTargetMarkets(info);
  
  // Extract goals and challenges
  const keyGoals = extractGoals(info);
  const challenges = extractChallenges(info);
  
  // Determine package recommendation
  const packageAnalysis = determinePackage(info, targetMarkets, keyGoals, challenges);
  
  // Determine business category and maturity
  const businessCategory = determineBusinessCategory(businessType, info);
  const businessMaturity = determineBusinessMaturity(info);

  return {
    businessName,
    businessType,
    businessCategory,
    currentSituation,
    targetMarkets,
    keyGoals,
    challenges,
    businessMaturity,
    recommendedPackage: packageAnalysis.package,
    confidence: packageAnalysis.confidence,
    reasoning: packageAnalysis.reasoning
  };
}

function extractBusinessName(info: string): string {
  // Look for business name patterns - improved for real emails
  const patterns = [
    // From URLs like loweryfinishing.com
    /(?:website|listing|business).*?([A-Z][a-z]+[A-Z][a-z]+)\.com/i,
    // From URLs like www.loweryfinishing.com
    /www\.([a-z]+)\.com/i,
    // Direct business names with common suffixes
    /([A-Z][a-z]+\s+[A-Z][a-z]+)(?:\s+(?:LLC|Inc|Corp|Finishing|Services|Company))?/,
    // Business name after "for" or "business"
    /(?:business|company|firm):\s*([^,\n]+)/i,
    /for\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)/i,
    // Lowery Finishing specific pattern
    /([A-Z][a-z]+)\s*[Ff]inishing/
  ];

  for (const pattern of patterns) {
    const match = info.match(pattern);
    if (match) {
      let name = match[1].trim();

      // Handle specific cases
      if (pattern.source.includes('www\\.')) {
        // From URL like www.loweryfinishing.com
        name = name.charAt(0).toUpperCase() + name.slice(1);
        if (info.toLowerCase().includes('finishing')) {
          name += ' Finishing';
        }
      } else if (name.toLowerCase() === 'lowery' && info.toLowerCase().includes('finishing')) {
        name = 'Lowery Finishing';
      } else if (name.toLowerCase().includes('finishing')) {
        // Proper case for finishing businesses
        name = name.split(' ').map(word =>
          word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
        ).join(' ');
      }

      return name;
    }
  }

  // Fallback: look for any capitalized words that might be business names
  const words = info.split(/\s+/);
  for (let i = 0; i < words.length - 1; i++) {
    if (words[i].match(/^[A-Z][a-z]+$/) && words[i + 1].match(/^[A-Z][a-z]+$/)) {
      return `${words[i]} ${words[i + 1]}`;
    }
  }

  return 'Your Business';
}

function extractBusinessType(info: string): string {
  const lowerInfo = info.toLowerCase();

  // Universal business type detection
  const businessTypes = {
    // Healthcare
    'dental': 'dental practice',
    'dentist': 'dental practice',
    'medical': 'medical practice',
    'doctor': 'medical practice',
    'veterinary': 'veterinary clinic',
    'vet': 'veterinary clinic',
    'chiropractic': 'chiropractic clinic',
    'physical therapy': 'physical therapy clinic',

    // Legal/Professional
    'law': 'law firm',
    'lawyer': 'law firm',
    'attorney': 'law firm',
    'accounting': 'accounting firm',
    'insurance': 'insurance agency',
    'real estate': 'real estate agency',

    // Home Services/Contractors
    'roofing': 'roofing contractor',
    'plumbing': 'plumbing services',
    'electrical': 'electrical services',
    'hvac': 'HVAC services',
    'landscaping': 'landscaping services',
    'cleaning': 'cleaning services',
    'painting': 'painting contractor',
    'flooring': 'flooring contractor',
    'door': 'door installation and finishing',
    'finishing': 'door installation and finishing',
    'trim': 'trim and millwork contractor',
    'millwork': 'trim and millwork contractor',
    'built-in': 'custom built-ins contractor',
    'cabinet': 'cabinet and built-ins contractor',
    'window': 'window installation',
    'kitchen': 'kitchen remodeling',
    'bathroom': 'bathroom remodeling',
    'construction': 'construction contractor',
    'handyman': 'handyman services',

    // Food & Hospitality
    'restaurant': 'restaurant',
    'bakery': 'bakery',
    'cafe': 'cafe',
    'catering': 'catering services',
    'food truck': 'food truck',

    // Retail & Services
    'salon': 'hair salon',
    'barber': 'barber shop',
    'spa': 'spa services',
    'fitness': 'fitness center',
    'gym': 'fitness center',
    'auto repair': 'auto repair shop',
    'mechanic': 'auto repair shop',
    'retail': 'retail store',
    'boutique': 'retail boutique'
  };

  // Find the most specific match
  for (const [keyword, type] of Object.entries(businessTypes)) {
    if (lowerInfo.includes(keyword)) return type;
  }

  return 'local business';
}

function determineBusinessCategory(businessType: string, info: string): 'service' | 'retail' | 'professional' | 'healthcare' | 'food' | 'contractor' {
  const lowerType = businessType.toLowerCase();
  const lowerInfo = info.toLowerCase();

  // Healthcare
  if (lowerType.includes('dental') || lowerType.includes('medical') || lowerType.includes('veterinary') ||
      lowerType.includes('chiropractic') || lowerType.includes('therapy')) {
    return 'healthcare';
  }

  // Professional Services
  if (lowerType.includes('law') || lowerType.includes('accounting') || lowerType.includes('insurance') ||
      lowerType.includes('real estate') || lowerType.includes('consulting')) {
    return 'professional';
  }

  // Food & Hospitality
  if (lowerType.includes('restaurant') || lowerType.includes('bakery') || lowerType.includes('cafe') ||
      lowerType.includes('catering') || lowerType.includes('food')) {
    return 'food';
  }

  // Contractors & Home Services
  if (lowerType.includes('contractor') || lowerType.includes('roofing') || lowerType.includes('plumbing') ||
      lowerType.includes('electrical') || lowerType.includes('hvac') || lowerType.includes('landscaping') ||
      lowerType.includes('painting') || lowerType.includes('construction') || lowerType.includes('installation')) {
    return 'contractor';
  }

  // Retail
  if (lowerType.includes('retail') || lowerType.includes('store') || lowerType.includes('boutique') ||
      lowerType.includes('shop') || lowerInfo.includes('sell') || lowerInfo.includes('products')) {
    return 'retail';
  }

  // Default to service
  return 'service';
}

function determineBusinessMaturity(info: string): 'startup' | 'growing' | 'established' {
  const lowerInfo = info.toLowerCase();

  // Established indicators
  if (lowerInfo.includes('review') || lowerInfo.includes('years') || lowerInfo.includes('established') ||
      lowerInfo.includes('experience') || lowerInfo.includes('reputation') || lowerInfo.includes('clients')) {
    return 'established';
  }

  // Startup indicators
  if (lowerInfo.includes('new') || lowerInfo.includes('starting') || lowerInfo.includes('launch') ||
      lowerInfo.includes('just opened') || lowerInfo.includes('no website')) {
    return 'startup';
  }

  // Default to growing
  return 'growing';
}

function analyzeSituation(info: string): string {
  const lowerInfo = info.toLowerCase();

  if (lowerInfo.includes('click funnel') || lowerInfo.includes('clickfunnels')) {
    return 'Currently using ClickFunnels but wants a legitimate, professional website';
  }
  if (lowerInfo.includes('no website') || lowerInfo.includes('need website')) {
    return 'No current website presence';
  }
  if (lowerInfo.includes('outdated') || lowerInfo.includes('old website')) {
    return 'Has an outdated website that needs modernization';
  }
  if (lowerInfo.includes('legit website') || lowerInfo.includes('legitimate website')) {
    return 'Wants to upgrade to a professional, credible website';
  }
  return 'Looking to improve online presence';
}

function extractTargetMarkets(info: string): string[] {
  const markets: string[] = [];
  const lowerInfo = info.toLowerCase();

  if (lowerInfo.includes('homeowner') || lowerInfo.includes('residential')) {
    markets.push('homeowners');
  }
  if (lowerInfo.includes('builder') || lowerInfo.includes('contractor') || lowerInfo.includes('commercial')) {
    markets.push('builders and contractors');
  }
  if (lowerInfo.includes('apartment') || lowerInfo.includes('custom home') || lowerInfo.includes('track home')) {
    if (!markets.includes('builders and contractors')) {
      markets.push('builders and contractors');
    }
  }
  if (lowerInfo.includes('business') && lowerInfo.includes('client')) {
    markets.push('business clients');
  }

  return markets.length > 0 ? markets : ['local customers'];
}

function extractGoals(info: string): string[] {
  const goals: string[] = [];
  const lowerInfo = info.toLowerCase();

  // Universal business goals
  if (lowerInfo.includes('professional') || lowerInfo.includes('credibility') || lowerInfo.includes('legit')) {
    goals.push('Establish a professional, credible online presence');
  }

  if (lowerInfo.includes('more customers') || lowerInfo.includes('more clients') || lowerInfo.includes('grow')) {
    goals.push('Attract more customers and grow the business');
  }

  if (lowerInfo.includes('appointment') || lowerInfo.includes('booking') || lowerInfo.includes('schedule')) {
    goals.push('Streamline appointment booking and scheduling');
  }

  if (lowerInfo.includes('quote') || lowerInfo.includes('estimate') || lowerInfo.includes('pricing')) {
    goals.push('Make it easier for customers to request quotes and pricing');
  }

  if (lowerInfo.includes('online ordering') || lowerInfo.includes('e-commerce') || lowerInfo.includes('sell online')) {
    goals.push('Enable online ordering and sales');
  }

  if (lowerInfo.includes('google') || lowerInfo.includes('search') || lowerInfo.includes('found online')) {
    goals.push('Improve search engine visibility and online discoverability');
  }

  if (lowerInfo.includes('showcase') || lowerInfo.includes('portfolio') || lowerInfo.includes('gallery')) {
    goals.push('Showcase work quality and build a professional portfolio');
  }

  if (lowerInfo.includes('reviews') || lowerInfo.includes('testimonials') || lowerInfo.includes('reputation')) {
    goals.push('Build and showcase customer reviews and testimonials');
  }

  if (lowerInfo.includes('mobile') || lowerInfo.includes('phone') || lowerInfo.includes('responsive')) {
    goals.push('Ensure website works perfectly on mobile devices');
  }

  if (lowerInfo.includes('social media') || lowerInfo.includes('facebook') || lowerInfo.includes('instagram')) {
    goals.push('Integrate and leverage social media presence');
  }

  return goals.length > 0 ? goals : ['Establish strong online presence and attract more customers'];
}

function extractChallenges(info: string): string[] {
  const challenges: string[] = [];
  const lowerInfo = info.toLowerCase();

  // Universal business challenges
  if (lowerInfo.includes('no website') || lowerInfo.includes('outdated website')) {
    challenges.push('Lacks professional website presence');
  }

  if (lowerInfo.includes('not getting found') || lowerInfo.includes('hard to find online')) {
    challenges.push('Poor online visibility and search rankings');
  }

  if (lowerInfo.includes('competition') || lowerInfo.includes('competitors')) {
    challenges.push('Standing out from local competition');
  }

  if (lowerInfo.includes('credibility') || lowerInfo.includes('trust') || lowerInfo.includes('professional')) {
    challenges.push('Building credibility and professional trust');
  }

  if (lowerInfo.includes('leads') || lowerInfo.includes('customers') || lowerInfo.includes('business is slow')) {
    challenges.push('Generating consistent leads and customers');
  }

  if (lowerInfo.includes('phone calls') || lowerInfo.includes('manual process') || lowerInfo.includes('time consuming')) {
    challenges.push('Inefficient manual processes taking too much time');
  }

  if (lowerInfo.includes('wrong customers') || lowerInfo.includes('not the right') || lowerInfo.includes('too broad')) {
    challenges.push('Attracting wrong type of customers or leads');
  }

  if (lowerInfo.includes('mobile') || lowerInfo.includes('phone') || lowerInfo.includes('responsive')) {
    challenges.push('Website not optimized for mobile users');
  }

  if (lowerInfo.includes('google') || lowerInfo.includes('reviews') || lowerInfo.includes('listing')) {
    challenges.push('Google Business Profile needs optimization');
  }

  if (lowerInfo.includes('social media') || lowerInfo.includes('online presence')) {
    challenges.push('Weak or inconsistent social media presence');
  }

  return challenges;
}

function determinePackage(info: string, targetMarkets: string[], goals: string[], challenges: string[]): {
  package: 'basic' | 'premium';
  confidence: number;
  reasoning: string[];
} {
  let premiumScore = 0;
  const reasoning: string[] = [];
  const lowerInfo = info.toLowerCase();

  // Multiple target markets favor premium (universal for any business)
  if (targetMarkets.length > 1) {
    premiumScore += 0.3;
    reasoning.push('Multiple target markets require dedicated pages and messaging for each audience');
  }

  // Professional credibility needs favor premium (universal)
  if (challenges.some(c => c.includes('credibility') || c.includes('trust')) ||
      goals.some(g => g.includes('credibility') || g.includes('professional'))) {
    premiumScore += 0.25;
    reasoning.push('Professional credibility requirements need comprehensive features and advanced presentation');
  }

  // Complex lead generation needs favor premium (universal)
  if (goals.some(g => g.includes('appointment') || g.includes('booking') || g.includes('quote')) ||
      challenges.some(c => c.includes('manual process') || c.includes('time consuming'))) {
    premiumScore += 0.2;
    reasoning.push('Advanced lead generation and automation features needed for efficient operations');
  }

  // SEO and visibility needs favor premium (universal)
  if (goals.some(g => g.includes('search') || g.includes('found online') || g.includes('visibility')) ||
      challenges.some(c => c.includes('visibility') || c.includes('competition') || c.includes('found'))) {
    premiumScore += 0.2;
    reasoning.push('Strong SEO strategy and advanced optimization needed to compete effectively');
  }

  // E-commerce or online sales favor premium (universal)
  if (goals.some(g => g.includes('online ordering') || g.includes('sell online') || g.includes('e-commerce'))) {
    premiumScore += 0.25;
    reasoning.push('E-commerce functionality and online sales features require premium capabilities');
  }

  // Established business indicators favor premium (universal)
  if (lowerInfo.includes('review') || lowerInfo.includes('years') || lowerInfo.includes('established') ||
      lowerInfo.includes('clients') || lowerInfo.includes('reputation') || lowerInfo.includes('experience')) {
    premiumScore += 0.15;
    reasoning.push('Established business can leverage advanced features and comprehensive SEO strategy');
  }

  const recommendedPackage = premiumScore >= 0.5 ? 'premium' : 'basic';

  if (reasoning.length === 0) {
    reasoning.push(recommendedPackage === 'premium'
      ? 'Business complexity and goals indicate need for comprehensive solution'
      : 'Basic package provides solid foundation for growth'
    );
  }

  return {
    package: recommendedPackage,
    confidence: Math.min(premiumScore + 0.2, 0.95), // Add base confidence
    reasoning
  };
}

function generateClientGreeting(analysis: CustomerAnalysis): string {
  return `Thank you for discussing your vision for ${analysis.businessName}. At GetFound, we specialize in helping ${analysis.businessType} businesses like yours enhance their online presence and attract their ideal customers. ${
    analysis.currentSituation.includes('ClickFunnels')
      ? `We understand you're looking to move beyond your current ClickFunnels page to a legitimate, professional website.`
      : analysis.currentSituation.includes('No current website')
      ? `We understand you're ready to establish a professional online presence for your business.`
      : `We understand you're looking to improve your current online presence with a more professional solution.`
  }`;
}

function generateSituationAnalysis(analysis: CustomerAnalysis): string {
  const markets = analysis.targetMarkets.join(' and ');

  let situationText = `We recognize that your business serves ${markets}, and you need a solution that speaks effectively to each audience.`;

  if (analysis.challenges.length > 0) {
    situationText += ` We also understand the key challenges you're facing: ${analysis.challenges.join('; ')}.`;
  }

  // Add category-specific insights
  switch (analysis.businessCategory) {
    case 'healthcare':
      situationText += ` As a healthcare practice, building patient trust and making it easy for patients to find and contact you is essential for growth.`;
      break;
    case 'professional':
      situationText += ` Professional service businesses like yours need to establish credibility and make it easy for clients to understand your expertise and get in touch.`;
      break;
    case 'contractor':
      situationText += ` Home service contractors need to showcase quality work, build trust with homeowners, and streamline the quote process for maximum efficiency.`;
      break;
    case 'food':
      situationText += ` Food businesses thrive on visual appeal, customer reviews, and making it easy for customers to find you and place orders.`;
      break;
    case 'retail':
      situationText += ` Retail businesses need to showcase products effectively and provide seamless online shopping experiences for customers.`;
      break;
    default:
      situationText += ` Your business needs a professional online presence that builds trust and makes it easy for customers to choose your services.`;
  }

  return situationText;
}

function generateGoalsSection(analysis: CustomerAnalysis): string {
  return `Based on our discussion, we've identified your key objectives:\n\n${
    analysis.keyGoals.map(goal => `• ${goal}`).join('\n')
  }`;
}

function generatePackageReasons(analysis: CustomerAnalysis): string[] {
  if (analysis.recommendedPackage === 'premium') {
    return [
      `Your ${analysis.targetMarkets.join(' and ')} focus requires dedicated service pages for maximum impact`,
      'Advanced SEO features will help you rank for specific services in your area',
      'Professional credibility features will build trust with your target market',
      'Comprehensive lead generation tools will streamline your quote process'
    ];
  } else {
    return [
      'Provides a solid professional foundation for your online presence',
      'GetFound app integration makes content creation effortless',
      'Cost-effective solution that grows with your business',
      'All essential features to establish credibility and generate leads'
    ];
  }
}

function generateBasicFeatures(analysis: CustomerAnalysis): string[] {
  return [
    `Custom 5-page website showcasing ${analysis.businessName}`,
    'GetFound mobile app for effortless content creation',
    'Professional portfolio section with filterable projects',
    'Mobile-responsive design for all devices',
    'Basic SEO setup and optimization',
    'Contact forms and quote request system',
    'Website hosting and maintenance included'
  ];
}

function generatePremiumFeatures(analysis: CustomerAnalysis): string[] {
  const features = [
    'Expanded 10-15+ page website with dedicated service pages',
    'Advanced SEO strategy targeting specific keywords',
    'Smart AI content placement on relevant service pages',
    'Enhanced lead generation system with custom forms',
    'Comprehensive Google Business Profile optimization',
    'Keyword rank tracking dashboard (launching September 2025)',
    'Professional credibility features for B2B clients'
  ];
  
  // Add market-specific features
  if (analysis.targetMarkets.includes('builders')) {
    features.push('Dedicated builder-focused service pages and testimonials');
  }
  if (analysis.targetMarkets.includes('homeowners')) {
    features.push('Homeowner-friendly quote request and measurement systems');
  }
  
  return features;
}

function generateSpecificSolutions(analysis: CustomerAnalysis): string[] {
  const solutions: string[] = [];

  // Universal solutions based on business category
  switch (analysis.businessCategory) {
    case 'healthcare':
      solutions.push('Patient-friendly appointment booking system with online scheduling');
      solutions.push('Professional practice pages highlighting specialties and credentials');
      solutions.push('Patient testimonials and review showcase for building trust');
      break;

    case 'professional':
      solutions.push('Professional service pages highlighting expertise and case studies');
      solutions.push('Client testimonials and credentials showcase for credibility');
      solutions.push('Contact forms optimized for consultation requests');
      break;

    case 'contractor':
      solutions.push('Before/after project galleries showcasing quality workmanship');
      solutions.push('Service-specific pages for different types of projects');
      solutions.push('Quote request system with project details collection');
      break;

    case 'food':
      solutions.push('Visual menu showcase with high-quality food photography');
      solutions.push('Online ordering system integration (if applicable)');
      solutions.push('Customer reviews and social media integration');
      break;

    case 'retail':
      solutions.push('Product catalog with professional photography');
      solutions.push('E-commerce functionality for online sales');
      solutions.push('Inventory management and customer account features');
      break;

    default:
      solutions.push('Professional service showcase tailored to your industry');
      solutions.push('Customer testimonials and portfolio presentation');
      solutions.push('Lead generation forms optimized for your business type');
  }

  // Universal solutions based on goals and challenges
  if (analysis.keyGoals.some((g: string) => g.includes('appointment') || g.includes('booking'))) {
    solutions.push('Online appointment booking and scheduling system');
  }

  if (analysis.keyGoals.some((g: string) => g.includes('quote') || g.includes('estimate'))) {
    solutions.push('Streamlined quote request system with automated follow-up');
  }

  if (analysis.challenges.some((c: string) => c.includes('Google') || c.includes('listing'))) {
    solutions.push('Google Business Profile optimization and management');
  }

  if (analysis.challenges.some((c: string) => c.includes('mobile') || c.includes('phone'))) {
    solutions.push('Mobile-optimized design ensuring perfect functionality on all devices');
  }

  if (analysis.keyGoals.some((g: string) => g.includes('social media'))) {
    solutions.push('Social media integration and automated content sharing');
  }

  return solutions.length > 0 ? solutions : [
    'Custom solutions tailored to your specific business needs',
    'Professional presentation that builds trust with your target market'
  ];
}

function generateNextSteps(analysis: CustomerAnalysis): string[] {
  return [
    'Schedule a follow-up call to discuss this proposal in detail',
    `Select the ${analysis.recommendedPackage} package that aligns with your business goals`,
    'Provide business assets and content for website development',
    'Begin implementation with our specialized team',
    'Launch your new professional online presence'
  ];
}

/**
 * Extracts questions from customer input and generates specific answers
 */
function extractQuestionsAndAnswers(customerInfo: string, analysis: CustomerAnalysis): CustomerQuestion[] {
  const questions: CustomerQuestion[] = [];
  const lowerInfo = customerInfo.toLowerCase();

  // Website-related questions
  if (lowerInfo.includes('website') || lowerInfo.includes('site')) {
    if (lowerInfo.includes('legit') || lowerInfo.includes('legitimate') || lowerInfo.includes('professional')) {
      questions.push({
        question: "How will you make my website look more professional and legitimate?",
        answer: `We'll create a custom, professional website that moves you beyond basic page builders like ClickFunnels. Your new site will feature modern design, professional photography integration through our GetFound app, and a structure that builds trust with ${analysis.targetMarkets.join(' and ')}.`,
        category: 'website'
      });
    }

    if (lowerInfo.includes('easier') && lowerInfo.includes('quote')) {
      questions.push({
        question: "How will the new website make it easier for customers to request quotes?",
        answer: `We'll implement streamlined quote request forms tailored to your ${analysis.businessType} business. ${analysis.businessCategory === 'contractor' ? 'For services like exterior door swaps, we can create measurement guides and photo upload systems to provide preliminary estimates without site visits.' : 'The forms will collect all necessary information upfront to provide accurate quotes quickly.'}`,
        category: 'website'
      });
    }
  }

  // Google-related questions
  if (lowerInfo.includes('google') || lowerInfo.includes('listing') || lowerInfo.includes('review')) {
    if (lowerInfo.includes('review') && lowerInfo.includes('builder')) {
      questions.push({
        question: "Should I ask builders for Google reviews, and how can I show credibility to them?",
        answer: "Yes, absolutely! Builder reviews are valuable. We recommend asking for testimonials that can be featured on your website's dedicated builder service pages. We'll also optimize your Google Business Profile and create professional case studies showcasing your work with builders to establish credibility.",
        category: 'google'
      });
    }

    if (lowerInfo.includes('picture') || lowerInfo.includes('photo') && lowerInfo.includes('automatic')) {
      questions.push({
        question: "How can I fix the automatic photo issue on my Google listing?",
        answer: "This is a common Google Business Profile issue. We'll help you upload and properly categorize your preferred photos, set a primary photo, and optimize your profile settings to control which images Google displays prominently.",
        category: 'google'
      });
    }
  }

  // Business-specific questions based on analysis
  if (analysis.businessCategory === 'contractor') {
    // Add contractor-specific questions
    if (lowerInfo.includes('door') && lowerInfo.includes('swap')) {
      questions.push({
        question: "How will you help me focus on exterior door swaps specifically?",
        answer: `We'll create a dedicated "Exterior Door Swaps" page that ranks well in search results. This page will include a measurement guide system for homeowners, clear pricing for site visits, and a streamlined quote process that qualifies leads before you travel to jobs.`,
        category: 'goals'
      });
    }

    if (lowerInfo.includes('builder') && lowerInfo.includes('credibility')) {
      questions.push({
        question: "How will you help me show credibility with builders?",
        answer: "We'll create dedicated service pages for each type of work you do for builders (interior doors, trim work, built-ins, etc.), showcase your comprehensive service list, and include builder testimonials and case studies. This professional presentation demonstrates your expertise and reliability.",
        category: 'goals'
      });
    }
  }

  // Pricing and process questions
  if (lowerInfo.includes('fee') || lowerInfo.includes('charge') || lowerInfo.includes('cost')) {
    questions.push({
      question: "What's included in the setup fee and monthly subscription?",
      answer: `The ${analysis.recommendedPackage} package includes ${analysis.recommendedPackage === 'premium' ? 'comprehensive website development, advanced SEO setup, and dedicated service pages' : 'professional 5-page website development and basic SEO setup'}. The monthly fee covers hosting, maintenance, GetFound app service, and ongoing support. ${analysis.recommendedPackage === 'premium' ? 'Premium also includes advanced SEO monitoring and reporting.' : ''}`,
      category: 'pricing'
    });
  }

  // If no specific questions found, add general ones based on business type
  if (questions.length === 0) {
    questions.push({
      question: `How will GetFound specifically help my ${analysis.businessType} business?`,
      answer: generateBusinessSpecificAnswer(analysis),
      category: 'general'
    });
  }

  return questions;
}

function generateBusinessSpecificAnswer(analysis: CustomerAnalysis): string {
  const baseAnswer = `GetFound specializes in ${analysis.businessCategory} businesses like yours. `;

  switch (analysis.businessCategory) {
    case 'contractor':
      return baseAnswer + `We'll create dedicated pages for each service you offer, implement project showcase galleries, and build lead generation systems that qualify customers before they contact you. Our GetFound app makes it easy to document completed work and automatically add it to your website.`;

    case 'healthcare':
      return baseAnswer + `We'll focus on patient trust-building features, appointment booking systems, and professional presentation of your credentials and services. The GetFound app helps you share patient success stories (with permission) and build your reputation.`;

    case 'professional':
      return baseAnswer + `We'll emphasize credibility-building features, case study presentation, and professional service descriptions. Your website will position you as the expert choice for clients seeking ${analysis.businessType} services.`;

    case 'food':
      return baseAnswer + `We'll showcase your menu with professional photography, implement online ordering capabilities if needed, and create systems for customer reviews and social media integration.`;

    case 'retail':
      return baseAnswer + `We'll create product showcases, implement e-commerce functionality, and build customer account features to drive repeat business.`;

    default:
      return baseAnswer + `We'll create a professional online presence that builds trust with your target market and makes it easy for customers to choose your services over competitors.`;
  }
}
