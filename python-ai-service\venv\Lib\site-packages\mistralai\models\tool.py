"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from .function import Function, FunctionTypedDict
from .tooltypes import ToolTypes
from mistralai.types import BaseModel
from mistralai.utils import validate_open_enum
from pydantic.functional_validators import PlainV<PERSON><PERSON><PERSON>
from typing import Optional
from typing_extensions import Annotated, NotRequired, TypedDict


class ToolTypedDict(TypedDict):
    function: FunctionTypedDict
    type: NotRequired[ToolTypes]


class Tool(BaseModel):
    function: Function

    type: Annotated[Optional[ToolTypes], PlainValidator(validate_open_enum(False))] = (
        None
    )
