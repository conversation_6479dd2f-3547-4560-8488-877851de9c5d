import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { pydanticAIService, DynamicProposalContent } from '@/lib/pydantic-ai-service';
import { generateProposalReferenceNumber } from '@/lib/reference-number-generator';

/**
 * Creates standard package structure with recommendation
 */
function createStandardPackages(recommendedPackage: 'basic' | 'premium') {
  return [
    {
      name: 'Basic "GetFound" Package',
      features: [
        'Custom 5-Page Professional Website',
        'GetFound Mobile App Integration',
        'Filterable Service Portfolio',
        'Mobile-Responsive Design',
        'Basic SEO Setup & Optimization',
        'Standard Quote Request Forms',
        'Website Hosting & Maintenance',
        'Google Business Profile Optimization'
      ],
      setupFee: '$499',
      monthlyFee: '$100',
      isRecommended: recommendedPackage === 'basic'
    },
    {
      name: 'Premium "GetFound" SEO & Growth Package',
      features: [
        'Everything in Basic Package',
        'Expanded Website (10-15+ Pages)',
        'Dedicated Service Pages for Each Offering',
        'Advanced SEO Strategy & Keyword Research',
        'Smart AI Content Builder & Placement',
        'Enhanced Lead Generation System',
        'Comprehensive Google Business Profile Management',
        'Local SEO Optimization for Your Service Area',
        'Keyword Rank Tracking Dashboard Access',
        'Ongoing SEO Reports & Performance Tracking'
      ],
      setupFee: '$2,999',
      monthlyFee: '$150',
      isRecommended: recommendedPackage === 'premium'
    }
  ];
}

export async function POST(request: NextRequest) {
  try {
    console.log('=== Dynamic Proposal Generation API ===');
    
    // Create Supabase client
    const cookieStore = cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });
    
    // Check authentication (optional for this endpoint)
    const { data: userData } = await supabase.auth.getUser();
    console.log('User authenticated:', !!userData.user);
    
    // Parse request body
    const body = await request.json();
    const { customerInfo, proposalId, businessName, businessType } = body;
    
    if (!customerInfo) {
      return NextResponse.json({ 
        error: 'Customer information is required',
        requiredFields: ['customerInfo']
      }, { status: 400 });
    }
    
    console.log('Generating dynamic proposal for:', businessName || 'Unknown Business');
    console.log('Customer info length:', customerInfo.length);

    // Step 1: Generate proposal using Pydantic AI
    const dynamicContent = await pydanticAIService.generateProposal(customerInfo);
    console.log('Generated dynamic content with', dynamicContent.questionsAndAnswers.length, 'Q&As');

    // Step 2: Get customer analysis
    const analysis = await pydanticAIService.analyzeCustomer(customerInfo);

    // Step 3: If proposalId provided, get existing proposal
    let existingProposal = null;
    if (proposalId) {
      const { data: proposalData } = await supabase
        .from('proposals')
        .select('proposal_data')
        .eq('id', proposalId)
        .single();

      if (proposalData) {
        existingProposal = proposalData.proposal_data;
      }
    }
    
    // Step 4: Create enhanced proposal structure with AI-generated content
    const enhancedProposal = {
      clientName: analysis.business_name,
      businessName: analysis.business_name,
      businessType: 'local business',
      overview: {
        projectTitle: dynamicContent.projectTitle,
        introduction: dynamicContent.acknowledgment,
        summary: analysis.current_situation
      },
      clientGoals: {
        title: "Understanding Your Goals",
        description: `Based on our discussion, we've identified your key objectives`,
        goals: analysis.key_needs
      },
      packages: createStandardPackages(dynamicContent.packageRecommendation.recommended),
      // Add AI-generated Q&A section
      specificQuestions: {
        title: "Addressing Your Specific Questions",
        questions: dynamicContent.questionsAndAnswers.map(qa => ({
          question: qa.question,
          answer: qa.answer,
          category: qa.category
        }))
      },
      // Add AI package recommendation with reasoning
      aiRecommendation: {
        recommendedPackage: dynamicContent.packageRecommendation.recommended,
        confidence: dynamicContent.packageRecommendation.confidence,
        reasoning: dynamicContent.packageRecommendation.whyThisPackage,
        title: "Our Recommendation Based on Your Needs"
      },
      nextSteps: dynamicContent.nextSteps,
      // Store original AI analysis for reference
      aiAnalysis: {
        businessName: analysis.business_name,
        currentSituation: analysis.current_situation,
        keyNeeds: analysis.key_needs,
        businessMaturity: analysis.business_maturity
      }
    };

    // Step 5: Save or update proposal if proposalId provided
    if (proposalId && userData.user) {
      const updateData = {
        proposal_data: enhancedProposal,
        business_name: analysis.business_name,
        business_type: 'local business',
        challenges: analysis.current_situation,
        notes: customerInfo,
        package_type: dynamicContent.packageRecommendation.recommended,
        updated_at: new Date().toISOString()
      };

      const { error: updateError } = await supabase
        .from('proposals')
        .update(updateData)
        .eq('id', proposalId);

      if (updateError) {
        console.error('Failed to update proposal:', updateError);
      } else {
        console.log('Successfully updated proposal:', proposalId);
      }
    }

    // Step 6: Return the enhanced proposal
    return NextResponse.json({
      success: true,
      dynamicContent: dynamicContent,
      updatedProposal: enhancedProposal,
      analysis: analysis,
      recommendations: {
        package: dynamicContent.packageRecommendation.recommended,
        confidence: dynamicContent.packageRecommendation.confidence,
        reasoning: dynamicContent.packageRecommendation.whyThisPackage
      },
      questionsAnswered: dynamicContent.questionsAndAnswers.length
    });
    
  } catch (error: any) {
    console.error('Dynamic proposal generation error:', error);
    return NextResponse.json({
      error: 'Failed to generate dynamic proposal',
      message: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    }, { status: 500 });
  }
}

export async function GET(request: NextRequest) {
  try {
    // Test endpoint with sample data
    const sampleCustomerInfo = `
1.) Website
  - current website (click funnel) https://www.loweryfinishing.com/optin-604655891694725683997
  - I would like to have a legit website that would make it even easier for customers to request quotes. I also feel it could be designed better to find the work that I want to do and not be so broad. I'll explain the work I want to find further in this email.

2.) Google listing
  - I have a few good reviews and try to get them each time we do a job for a customer. Usually we are doing work for builders and don't ask for a review. But should we? Is there a better way to show credibility with big builders?
  - One of the pictures from a review automatically shows up with the listing which is kind of annoying...Not sure how to fix this.

3.) Goals
  A. Homeowners
      - exterior door swaps. Not repairs, or anything else. Just straight swaps. This is one I would like to highlight in particular. Good money maker for us. I would also like to charge a fee to bid if we need to come out to take measurements. Would be nice to have a system with instructions on how to give us information so we can give them an easy free estimate without driving to the job.
      - Everything else listed under (B) as well.

 B.) Builders (apartments, custom homes, track homes)
      We Install the following below:
Exterior Doors, Interior Doors, Pocket doors, Bi Fold Doors, Bi Pass Doors, Barndoor, Transoms, Cased Opening, Window surrounds, Window sills, Window mulls, Closet Shelf, Closet Shelf face framed, Closet Linen, Closet Linen face framed, Base, Base Shoe, Treads & Risers, Skirt Boards, Wall cap, Handrail, Door and bath hardware, Attic access doors, Open Rail, Newel Post, Deco Shelf, Mantel, Built in bookcases, Built in bench, Wainscot, built in desk / sub top for counter, built in Lockers, Setting Exterior Doors, Setting Pocket Door frames
`;

    console.log('Testing Pydantic AI service...');

    // Test both analysis and proposal generation
    const analysis = await pydanticAIService.analyzeCustomer(sampleCustomerInfo);
    const dynamicContent = await pydanticAIService.generateProposal(sampleCustomerInfo);

    return NextResponse.json({
      success: true,
      message: 'Pydantic AI proposal generator test',
      sampleAnalysis: {
        businessName: analysis.business_name,
        situation: analysis.current_situation,
        maturity: analysis.business_maturity,
        needs: analysis.key_needs
      },
      proposalData: {
        projectTitle: dynamicContent.projectTitle,
        packageRecommendation: dynamicContent.packageRecommendation,
        questionsFound: dynamicContent.questionsAndAnswers.length,
        questions: dynamicContent.questionsAndAnswers.map(qa => ({
          question: qa.question,
          category: qa.category
        }))
      },
      fullDynamicContent: dynamicContent
    });

  } catch (error: any) {
    console.error('Test endpoint error:', error);
    return NextResponse.json({
      error: 'Test failed',
      message: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    }, { status: 500 });
  }
}
