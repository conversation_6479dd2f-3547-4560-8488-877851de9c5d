import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { generateAIEnhancedProposal, AIProposalRequest } from '@/lib/ai-dynamic-proposal-service';
import { generateDynamicProposal } from '@/lib/dynamic-proposal-generator';

export async function POST(request: NextRequest) {
  try {
    console.log('=== Dynamic Proposal Generation API ===');
    
    // Create Supabase client
    const cookieStore = cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });
    
    // Check authentication (optional for this endpoint)
    const { data: userData } = await supabase.auth.getUser();
    console.log('User authenticated:', !!userData.user);
    
    // Parse request body
    const body = await request.json();
    const { customerInfo, proposalId, businessName, businessType } = body;
    
    if (!customerInfo) {
      return NextResponse.json({ 
        error: 'Customer information is required',
        requiredFields: ['customerInfo']
      }, { status: 400 });
    }
    
    console.log('Generating dynamic proposal for:', businessName || 'Unknown Business');
    console.log('Customer info length:', customerInfo.length);
    
    // Step 1: Generate base dynamic content
    const dynamicContent = generateDynamicProposal(customerInfo);
    console.log('Generated dynamic content with', dynamicContent.questionsAndAnswers.length, 'Q&As');
    
    // Step 2: If proposalId provided, get existing proposal
    let existingProposal = null;
    if (proposalId) {
      const { data: proposalData } = await supabase
        .from('proposals')
        .select('proposal_data')
        .eq('id', proposalId)
        .single();
      
      if (proposalData) {
        existingProposal = proposalData.proposal_data;
      }
    }
    
    // Step 3: Generate AI-enhanced proposal
    const aiRequest: AIProposalRequest = {
      customerInfo,
      businessName,
      businessType,
      existingProposal
    };
    
    const aiResponse = await generateAIEnhancedProposal(aiRequest);
    
    // Step 4: Save or update proposal if proposalId provided
    if (proposalId && userData.user) {
      const updateData = {
        proposal_data: aiResponse.updatedProposal,
        business_name: aiResponse.analysis.businessName,
        business_type: aiResponse.analysis.businessType,
        challenges: aiResponse.analysis.challenges.join('; '),
        notes: customerInfo,
        package_type: aiResponse.analysis.recommendedPackage,
        updated_at: new Date().toISOString()
      };
      
      const { error: updateError } = await supabase
        .from('proposals')
        .update(updateData)
        .eq('id', proposalId);
      
      if (updateError) {
        console.error('Failed to update proposal:', updateError);
      } else {
        console.log('Successfully updated proposal:', proposalId);
      }
    }
    
    // Step 5: Return the enhanced proposal
    return NextResponse.json({
      success: true,
      dynamicContent: aiResponse.dynamicContent,
      updatedProposal: aiResponse.updatedProposal,
      analysis: aiResponse.analysis,
      recommendations: {
        package: aiResponse.analysis.recommendedPackage,
        confidence: aiResponse.analysis.confidence,
        reasoning: aiResponse.analysis.reasoning
      },
      questionsAnswered: aiResponse.dynamicContent.questionsAndAnswers.length
    });
    
  } catch (error: any) {
    console.error('Dynamic proposal generation error:', error);
    return NextResponse.json({
      error: 'Failed to generate dynamic proposal',
      message: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    }, { status: 500 });
  }
}

export async function GET(request: NextRequest) {
  try {
    // Test endpoint with sample data
    const sampleCustomerInfo = `
1.) Website
  - current website (click funnel) https://www.loweryfinishing.com/optin-604655891694725683997
  - I would like to have a legit website that would make it even easier for customers to request quotes. I also feel it could be designed better to find the work that I want to do and not be so broad. I'll explain the work I want to find further in this email.

2.) Google listing 
  - I have a few good reviews and try to get them each time we do a job for a customer. Usually we are doing work for builders and don't ask for a review. But should we? Is there a better way to show credibility with big builders?
  - One of the pictures from a review automatically shows up with the listing which is kind of annoying...Not sure how to fix this.

3.) Goals
  A. Homeowners
      - exterior door swaps. Not repairs, or anything else. Just straight swaps. This is one I would like to highlight in particular. Good money maker for us. I would also like to charge a fee to bid if we need to come out to take measurements. Would be nice to have a system with instructions on how to give us information so we can give them an easy free estimate without driving to the job.
      - Everything else listed under (B) as well.
 
 B.) Builders (apartments, custom homes, track homes)
      We Install the following below:
Exterior Doors	
Interior Doors	
Pocket doors	
Bi Fold Doors	
Bi Pass Doors	
Barndoor	
Transoms	
Cased Opening	
Window surrounds	
Window sills	
Window mulls	
Closet Shelf	
Closet Shelf	face framed
Closet Linen	
Closet Linen	face framed
Base	
Base Shoe	
Treads & Risers	
Skirt Boards	
Wall cap	
Handrail	
Door, and bath hardware	
Attic access doors
Open Rail	
Newel Post	
Deco Shelf	
Mantel	
Built in bookcases	
Built in bench	
Wainscot	
built in desk / sub top for counter	
built in Lockers	
Setting Exterior Doors
Setting Pocket Door frames
`;
    
    const dynamicContent = generateDynamicProposal(sampleCustomerInfo);
    
    return NextResponse.json({
      success: true,
      message: 'Dynamic proposal generator test',
      sampleAnalysis: {
        businessName: dynamicContent.projectTitle,
        packageRecommendation: dynamicContent.packageRecommendation,
        questionsFound: dynamicContent.questionsAndAnswers.length,
        questions: dynamicContent.questionsAndAnswers
      },
      dynamicContent
    });
    
  } catch (error: any) {
    console.error('Test endpoint error:', error);
    return NextResponse.json({
      error: 'Test failed',
      message: error.message
    }, { status: 500 });
  }
}
